// Link Preview Service
class PreviewService {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = getConfig('PREVIEW.CACHE_DURATION', 24) * 60 * 60 * 1000; // Convert to ms
        this.requestTimeout = getConfig('API.REQUEST_TIMEOUT', 10000);
        this.maxRetries = getConfig('API.MAX_RETRIES', 3);
        this.retryDelay = getConfig('API.RETRY_DELAY', 1000);
        
        this.loadCacheFromStorage();
    }

    /**
     * Get preview data for a URL
     * @param {string} url - URL to get preview for
     * @param {boolean} useCache - Whether to use cached data
     */
    async getPreview(url, useCache = true) {
        if (!url || !this.isValidUrl(url)) {
            throw new Error('Invalid URL provided');
        }

        const normalizedUrl = this.normalizeUrl(url);
        
        // Check cache first
        if (useCache && this.cache.has(normalizedUrl)) {
            const cached = this.cache.get(normalizedUrl);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                return cached.data;
            } else {
                this.cache.delete(normalizedUrl);
            }
        }

        try {
            const preview = await this.fetchPreview(normalizedUrl);
            
            // Cache the result
            this.cache.set(normalizedUrl, {
                data: preview,
                timestamp: Date.now()
            });
            
            this.saveCacheToStorage();
            return preview;
        } catch (error) {
            console.error('Failed to fetch preview:', error);
            
            // Return basic preview data
            return this.createFallbackPreview(url);
        }
    }

    /**
     * Fetch preview from API
     * @private
     */
    async fetchPreview(url) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                // Try Microlink API first
                const preview = await this.fetchFromMicrolink(url);
                if (preview) return preview;
                
                // Fallback to manual parsing
                return await this.fetchManually(url);
                
            } catch (error) {
                lastError = error;
                console.warn(`Preview fetch attempt ${attempt} failed:`, error);
                
                if (attempt < this.maxRetries) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Fetch preview using Microlink API
     * @private
     */
    async fetchFromMicrolink(url) {
        const apiUrl = new URL(getConfig('API.MICROLINK_BASE_URL'));
        apiUrl.searchParams.set('url', url);
        apiUrl.searchParams.set('screenshot', 'true');
        apiUrl.searchParams.set('meta', 'false');
        apiUrl.searchParams.set('embed', 'screenshot.url');
        
        const apiKey = getConfig('API.MICROLINK_API_KEY');
        if (apiKey) {
            apiUrl.searchParams.set('key', apiKey);
        }

        const response = await this.fetchWithTimeout(apiUrl.toString());
        
        if (!response.ok) {
            throw new Error(`Microlink API error: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error('Microlink API returned error status');
        }

        return this.normalizeMicrolinkData(data.data);
    }

    /**
     * Fetch preview manually by parsing HTML
     * @private
     */
    async fetchManually(url) {
        // Use a CORS proxy for client-side requests
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
        
        const response = await this.fetchWithTimeout(proxyUrl);
        
        if (!response.ok) {
            throw new Error(`Failed to fetch URL: ${response.status}`);
        }

        const data = await response.json();
        const html = data.contents;
        
        return this.parseHtmlForPreview(html, url);
    }

    /**
     * Parse HTML for Open Graph and meta tags
     * @private
     */
    parseHtmlForPreview(html, url) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        const getMetaContent = (property, attribute = 'property') => {
            const meta = doc.querySelector(`meta[${attribute}="${property}"]`);
            return meta ? meta.getAttribute('content') : null;
        };

        const title = getMetaContent('og:title') || 
                     getMetaContent('twitter:title') || 
                     doc.querySelector('title')?.textContent ||
                     this.extractDomainFromUrl(url);

        const description = getMetaContent('og:description') || 
                           getMetaContent('twitter:description') || 
                           getMetaContent('description', 'name') ||
                           '';

        const image = getMetaContent('og:image') || 
                     getMetaContent('twitter:image') ||
                     getMetaContent('twitter:image:src') ||
                     null;

        const siteName = getMetaContent('og:site_name') || 
                        this.extractDomainFromUrl(url);

        return {
            title: this.cleanText(title),
            description: this.cleanText(description),
            image: image ? this.resolveImageUrl(image, url) : null,
            siteName: this.cleanText(siteName),
            url: url,
            favicon: this.getFaviconUrl(url)
        };
    }

    /**
     * Normalize Microlink API response
     * @private
     */
    normalizeMicrolinkData(data) {
        return {
            title: data.title || this.extractDomainFromUrl(data.url),
            description: data.description || '',
            image: data.screenshot?.url || data.image?.url || null,
            siteName: data.publisher || this.extractDomainFromUrl(data.url),
            url: data.url,
            favicon: this.getFaviconUrl(data.url)
        };
    }

    /**
     * Create fallback preview data
     * @private
     */
    createFallbackPreview(url) {
        return {
            title: this.extractDomainFromUrl(url),
            description: '',
            image: getConfig('PREVIEW.FALLBACK_IMAGE'),
            siteName: this.extractDomainFromUrl(url),
            url: url,
            favicon: this.getFaviconUrl(url)
        };
    }

    /**
     * Fetch with timeout
     * @private
     */
    async fetchWithTimeout(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
                headers: {
                    'User-Agent': 'Link Manager Pro/1.0',
                    ...options.headers
                }
            });
            
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * Utility functions
     */
    
    isValidUrl(url) {
        return getConfig('VALIDATION.URL_PATTERN').test(url);
    }

    normalizeUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.href;
        } catch {
            return url;
        }
    }

    extractDomainFromUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname.replace(/^www\./, '');
        } catch {
            return url;
        }
    }

    getFaviconUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
        } catch {
            return null;
        }
    }

    resolveImageUrl(imageUrl, baseUrl) {
        try {
            return new URL(imageUrl, baseUrl).href;
        } catch {
            return imageUrl;
        }
    }

    cleanText(text) {
        if (!text) return '';
        return text.trim().replace(/\s+/g, ' ').substring(0, 500);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Cache management
     */
    
    loadCacheFromStorage() {
        try {
            const cached = storageManager.get('preview_cache', {});
            this.cache = new Map(Object.entries(cached));
        } catch (error) {
            console.warn('Failed to load preview cache:', error);
            this.cache = new Map();
        }
    }

    saveCacheToStorage() {
        try {
            const cacheObj = Object.fromEntries(this.cache);
            storageManager.set('preview_cache', cacheObj);
        } catch (error) {
            console.warn('Failed to save preview cache:', error);
        }
    }

    clearCache() {
        this.cache.clear();
        storageManager.remove('preview_cache');
    }

    getCacheSize() {
        return this.cache.size;
    }

    /**
     * Batch preview fetching
     */
    async getMultiplePreviews(urls, onProgress = null) {
        const results = [];
        const total = urls.length;
        
        for (let i = 0; i < urls.length; i++) {
            try {
                const preview = await this.getPreview(urls[i]);
                results.push({ url: urls[i], preview, error: null });
            } catch (error) {
                results.push({ url: urls[i], preview: null, error: error.message });
            }
            
            if (onProgress) {
                onProgress(i + 1, total);
            }
        }
        
        return results;
    }
}

// Create global instance
const previewService = new PreviewService();
