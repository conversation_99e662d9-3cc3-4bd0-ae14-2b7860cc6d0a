// Sidebar Component Management
class SidebarManager {
    constructor() {
        this.isCollapsed = false;
        this.isMobile = window.innerWidth < 1024;
        this.setupEventListeners();
        this.updateLayout();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Window resize handler
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth < 1024;
            
            if (wasMobile !== this.isMobile) {
                this.updateLayout();
            }
        });

        // Category change handler
        window.addEventListener('category-changed', (e) => {
            this.updateActiveCategory(e.detail.categoryId);
        });

        // Storage change handler for link counts
        window.addEventListener('storage-change', (e) => {
            if (e.detail.key === storageManager.keys.LINKS) {
                this.updateLinkCounts();
            }
        });

        // Mobile menu toggle
        this.setupMobileMenuToggle();
    }

    /**
     * Setup mobile menu toggle
     */
    setupMobileMenuToggle() {
        // Create mobile menu button if it doesn't exist
        let mobileMenuBtn = document.getElementById('mobileMenuBtn');
        
        if (!mobileMenuBtn && this.isMobile) {
            mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.id = 'mobileMenuBtn';
            mobileMenuBtn.className = 'lg:hidden glass-button p-3 rounded-xl';
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars text-white"></i>';
            
            // Insert after settings button
            const settingsBtn = document.getElementById('settingsBtn');
            if (settingsBtn) {
                settingsBtn.parentNode.insertBefore(mobileMenuBtn, settingsBtn.nextSibling);
            }
        }

        // Add click handler
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }
    }

    /**
     * Toggle mobile sidebar
     */
    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        const isVisible = sidebar.classList.contains('mobile-visible');
        
        if (isVisible) {
            this.hideMobileSidebar();
        } else {
            this.showMobileSidebar();
        }
    }

    /**
     * Show mobile sidebar
     */
    showMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        // Create overlay
        const overlay = document.createElement('div');
        overlay.id = 'sidebarOverlay';
        overlay.className = 'fixed inset-0 bg-black/50 z-40 lg:hidden';
        overlay.addEventListener('click', () => this.hideMobileSidebar());
        document.body.appendChild(overlay);

        // Show sidebar
        sidebar.classList.add('mobile-visible');
        sidebar.style.transform = 'translateX(0)';
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    /**
     * Hide mobile sidebar
     */
    hideMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        if (sidebar) {
            sidebar.classList.remove('mobile-visible');
            sidebar.style.transform = '';
        }
        
        if (overlay) {
            overlay.remove();
        }
        
        // Restore body scroll
        document.body.style.overflow = '';
    }

    /**
     * Update layout based on screen size
     */
    updateLayout() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        if (this.isMobile) {
            // Mobile layout
            sidebar.classList.add('fixed', 'top-0', 'left-0', 'h-full', 'z-50', 'transform', '-translate-x-full', 'transition-transform', 'duration-300');
            sidebar.classList.remove('lg:w-80');
            sidebar.style.width = '280px';
            
            this.setupMobileMenuToggle();
        } else {
            // Desktop layout
            sidebar.classList.remove('fixed', 'top-0', 'left-0', 'h-full', 'z-50', 'transform', '-translate-x-full', 'transition-transform', 'duration-300', 'mobile-visible');
            sidebar.classList.add('lg:w-80');
            sidebar.style.width = '';
            sidebar.style.transform = '';
            
            // Remove mobile menu button
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            if (mobileMenuBtn) {
                mobileMenuBtn.remove();
            }
            
            // Remove overlay if exists
            const overlay = document.getElementById('sidebarOverlay');
            if (overlay) {
                overlay.remove();
            }
        }
    }

    /**
     * Update active category in sidebar
     */
    updateActiveCategory(categoryId) {
        const categoryItems = document.querySelectorAll('.category-item');
        
        categoryItems.forEach(item => {
            const itemCategoryId = item.getAttribute('data-category-id');
            
            if (itemCategoryId === categoryId) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Update link counts in categories
     */
    updateLinkCounts() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categories = categoryManager.getCategories();
        
        // Count links per category
        const counts = {};
        links.forEach(link => {
            const category = link.category || 'uncategorized';
            counts[category] = (counts[category] || 0) + 1;
        });

        // Update category displays
        categories.forEach(category => {
            const categoryElement = document.querySelector(`[data-category-id="${category.id}"]`);
            if (categoryElement) {
                const countElement = categoryElement.querySelector('.text-gray-400');
                if (countElement) {
                    let count = 0;
                    if (category.id === 'all') {
                        count = links.length;
                    } else if (category.id === 'favorites') {
                        count = links.filter(link => link.favorite).length;
                    } else {
                        count = counts[category.id] || 0;
                    }
                    countElement.textContent = `${count} links`;
                }
            }
        });
    }

    /**
     * Render sidebar statistics
     */
    renderStats() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categories = categoryManager.getCategories();
        
        const stats = {
            totalLinks: links.length,
            totalCategories: categories.filter(cat => cat.id !== 'all').length,
            favoriteLinks: links.filter(link => link.favorite).length,
            recentLinks: links.filter(link => {
                const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                return new Date(link.createdAt) > dayAgo;
            }).length
        };

        // Find or create stats container
        let statsContainer = document.getElementById('sidebarStats');
        
        if (!statsContainer) {
            statsContainer = document.createElement('div');
            statsContainer.id = 'sidebarStats';
            statsContainer.className = 'mt-8 p-4 glass-card rounded-xl';
            
            // Insert before add link button
            const addLinkBtn = document.getElementById('addLinkBtn');
            if (addLinkBtn) {
                addLinkBtn.parentNode.insertBefore(statsContainer, addLinkBtn);
            }
        }

        statsContainer.innerHTML = `
            <h3 class="text-sm font-semibold text-gray-300 mb-3">Quick Stats</h3>
            <div class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Total Links</span>
                    <span class="text-white font-medium">${stats.totalLinks}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Categories</span>
                    <span class="text-white font-medium">${stats.totalCategories}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Favorites</span>
                    <span class="text-white font-medium">${stats.favoriteLinks}</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-400">Added Today</span>
                    <span class="text-white font-medium">${stats.recentLinks}</span>
                </div>
            </div>
        `;
    }

    /**
     * Add quick actions to sidebar
     */
    renderQuickActions() {
        let actionsContainer = document.getElementById('quickActions');
        
        if (!actionsContainer) {
            actionsContainer = document.createElement('div');
            actionsContainer.id = 'quickActions';
            actionsContainer.className = 'mt-6 space-y-2';
            
            // Insert after categories
            const categoryList = document.getElementById('categoryList');
            if (categoryList) {
                categoryList.parentNode.insertBefore(actionsContainer, categoryList.nextSibling);
            }
        }

        actionsContainer.innerHTML = `
            <button id="importLinksBtn" class="w-full glass-button p-3 rounded-xl text-white text-sm hover:scale-105 transition-transform">
                <i class="fas fa-upload mr-2"></i>
                Import Links
            </button>
            <button id="exportLinksBtn" class="w-full glass-button p-3 rounded-xl text-white text-sm hover:scale-105 transition-transform">
                <i class="fas fa-download mr-2"></i>
                Export Links
            </button>
        `;

        // Add event listeners
        document.getElementById('importLinksBtn')?.addEventListener('click', () => {
            window.dispatchEvent(new CustomEvent('show-import-modal'));
        });

        document.getElementById('exportLinksBtn')?.addEventListener('click', () => {
            window.dispatchEvent(new CustomEvent('show-export-modal'));
        });
    }

    /**
     * Collapse/expand sidebar (desktop only)
     */
    toggleCollapse() {
        if (this.isMobile) return;

        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        this.isCollapsed = !this.isCollapsed;
        
        if (this.isCollapsed) {
            sidebar.classList.add('collapsed');
            sidebar.style.width = '80px';
        } else {
            sidebar.classList.remove('collapsed');
            sidebar.style.width = '';
        }

        // Dispatch event for other components to adjust
        window.dispatchEvent(new CustomEvent('sidebar-toggle', {
            detail: { collapsed: this.isCollapsed }
        }));
    }

    /**
     * Initialize sidebar
     */
    init() {
        this.updateLayout();
        this.renderStats();
        this.renderQuickActions();
        this.updateLinkCounts();
        
        // Update stats periodically
        setInterval(() => {
            this.renderStats();
        }, 30000); // Every 30 seconds
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(event) {
        // Close mobile sidebar on Escape
        if (event.key === 'Escape' && this.isMobile) {
            this.hideMobileSidebar();
        }
    }

    /**
     * Search within categories
     */
    filterCategories(searchTerm) {
        const categoryItems = document.querySelectorAll('.category-item');
        const term = searchTerm.toLowerCase();
        
        categoryItems.forEach(item => {
            const categoryName = item.querySelector('.text-white').textContent.toLowerCase();
            
            if (categoryName.includes(term) || term === '') {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }
}

// Create global instance
const sidebarManager = new SidebarManager();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    sidebarManager.init();
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    sidebarManager.handleKeyboardShortcuts(e);
});
