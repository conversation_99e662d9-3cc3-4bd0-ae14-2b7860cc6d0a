// Link Card Component
class LinkCard {
    constructor(link, options = {}) {
        this.link = link;
        this.options = {
            showCategory: true,
            showActions: true,
            showPreview: true,
            ...options
        };
    }

    /**
     * Render the link card element
     */
    render() {
        const card = document.createElement('div');
        card.className = 'link-card glass-card rounded-2xl p-6 fade-in';
        card.setAttribute('data-link-id', this.link.id);

        card.innerHTML = this.getCardHTML();
        this.setupEventListeners(card);
        
        return card;
    }

    /**
     * Get card HTML content
     */
    getCardHTML() {
        const { link } = this;
        const preview = link.preview || {};
        const category = categoryManager.getCategory(link.category);
        
        return `
            <div class="relative">
                ${this.options.showActions ? this.getActionsHTML() : ''}
                
                ${this.options.showPreview && preview.image ? `
                    <div class="preview-image-container mb-4 relative overflow-hidden rounded-xl">
                        <img src="${preview.image}" 
                             alt="${this.escapeHtml(link.title)}"
                             class="preview-image w-full h-48 object-cover"
                             loading="lazy"
                             onerror="this.style.display='none'">
                        ${link.favorite ? `
                            <div class="absolute top-3 right-3">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-star text-white text-sm"></i>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
                
                <div class="space-y-3">
                    <div class="flex items-start justify-between">
                        <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                ${this.escapeHtml(link.title || link.url)}
                            </h3>
                            
                            ${link.description ? `
                                <p class="text-gray-300 text-sm line-clamp-3 mb-3">
                                    ${this.escapeHtml(link.description)}
                                </p>
                            ` : ''}
                        </div>
                        
                        ${!this.options.showPreview && link.favorite ? `
                            <div class="ml-3">
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="flex items-center space-x-2 text-sm">
                        ${preview.favicon ? `
                            <img src="${preview.favicon}" 
                                 alt="Favicon" 
                                 class="favicon w-4 h-4"
                                 onerror="this.style.display='none'">
                        ` : ''}
                        <span class="text-gray-400 truncate">
                            ${this.getDomainFromUrl(link.url)}
                        </span>
                    </div>
                    
                    ${this.options.showCategory && category ? `
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 rounded flex items-center justify-center" 
                                 style="background: ${category.color}20; border: 1px solid ${category.color}40;">
                                <i class="${category.icon} text-xs" style="color: ${category.color};"></i>
                            </div>
                            <span class="text-xs text-gray-400">${this.escapeHtml(category.name)}</span>
                        </div>
                    ` : ''}
                    
                    ${link.tags && link.tags.length > 0 ? `
                        <div class="flex flex-wrap gap-1">
                            ${link.tags.slice(0, 3).map(tag => `
                                <span class="px-2 py-1 bg-white/10 rounded-full text-xs text-gray-300">
                                    ${this.escapeHtml(tag)}
                                </span>
                            `).join('')}
                            ${link.tags.length > 3 ? `
                                <span class="px-2 py-1 bg-white/10 rounded-full text-xs text-gray-400">
                                    +${link.tags.length - 3} more
                                </span>
                            ` : ''}
                        </div>
                    ` : ''}
                    
                    <div class="flex items-center justify-between pt-3 border-t border-white/10">
                        <div class="flex items-center space-x-3">
                            <button class="open-link-btn glass-button px-4 py-2 rounded-lg text-sm text-white hover:scale-105 transition-transform">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Open
                            </button>
                            <button class="copy-link-btn glass-button p-2 rounded-lg text-white hover:scale-105 transition-transform">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        
                        <div class="text-xs text-gray-500">
                            ${this.formatDate(link.createdAt)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get actions dropdown HTML
     */
    getActionsHTML() {
        return `
            <div class="absolute top-4 right-4 z-10">
                <div class="dropdown relative">
                    <button class="actions-btn glass-button p-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
                        <i class="fas fa-ellipsis-v text-white"></i>
                    </button>
                    <div class="dropdown-menu absolute right-0 top-full mt-2 w-48 glass-card rounded-xl p-2 opacity-0 invisible transition-all duration-200 transform scale-95">
                        <button class="edit-link-btn w-full text-left px-3 py-2 rounded-lg text-white hover:bg-white/10 transition-colors">
                            <i class="fas fa-edit mr-3"></i>
                            Edit Link
                        </button>
                        <button class="toggle-favorite-btn w-full text-left px-3 py-2 rounded-lg text-white hover:bg-white/10 transition-colors">
                            <i class="fas ${this.link.favorite ? 'fa-star' : 'fa-star-o'} mr-3"></i>
                            ${this.link.favorite ? 'Remove from Favorites' : 'Add to Favorites'}
                        </button>
                        <button class="duplicate-link-btn w-full text-left px-3 py-2 rounded-lg text-white hover:bg-white/10 transition-colors">
                            <i class="fas fa-clone mr-3"></i>
                            Duplicate
                        </button>
                        <hr class="border-white/20 my-2">
                        <button class="delete-link-btn w-full text-left px-3 py-2 rounded-lg text-red-400 hover:bg-red-500/20 transition-colors">
                            <i class="fas fa-trash mr-3"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners for the card
     */
    setupEventListeners(card) {
        // Add hover effect class
        card.classList.add('group');

        // Open link
        const openBtn = card.querySelector('.open-link-btn');
        openBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.openLink();
        });

        // Copy link
        const copyBtn = card.querySelector('.copy-link-btn');
        copyBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.copyLink();
        });

        // Actions dropdown
        const actionsBtn = card.querySelector('.actions-btn');
        const dropdown = card.querySelector('.dropdown-menu');
        
        if (actionsBtn && dropdown) {
            actionsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(dropdown);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!card.contains(e.target)) {
                    this.closeDropdown(dropdown);
                }
            });
        }

        // Edit link
        const editBtn = card.querySelector('.edit-link-btn');
        editBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.editLink();
        });

        // Toggle favorite
        const favoriteBtn = card.querySelector('.toggle-favorite-btn');
        favoriteBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleFavorite();
        });

        // Duplicate link
        const duplicateBtn = card.querySelector('.duplicate-link-btn');
        duplicateBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.duplicateLink();
        });

        // Delete link
        const deleteBtn = card.querySelector('.delete-link-btn');
        deleteBtn?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteLink();
        });

        // Card click to open link
        card.addEventListener('click', () => {
            this.openLink();
        });

        // Keyboard navigation
        card.setAttribute('tabindex', '0');
        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.openLink();
            }
        });
    }

    /**
     * Action methods
     */
    
    openLink() {
        window.open(this.link.url, '_blank', 'noopener,noreferrer');
        
        // Track click (could be used for analytics)
        this.trackLinkClick();
    }

    async copyLink() {
        try {
            await navigator.clipboard.writeText(this.link.url);
            showSuccess('Link copied to clipboard');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.link.url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showSuccess('Link copied to clipboard');
        }
    }

    editLink() {
        modalManager.showLinkForm(
            this.link,
            (linkData) => {
                window.dispatchEvent(new CustomEvent('link-update', {
                    detail: { id: this.link.id, data: linkData }
                }));
            }
        );
    }

    toggleFavorite() {
        const updatedLink = { ...this.link, favorite: !this.link.favorite };
        window.dispatchEvent(new CustomEvent('link-update', {
            detail: { id: this.link.id, data: updatedLink }
        }));
        
        showSuccess(updatedLink.favorite ? 'Added to favorites' : 'Removed from favorites');
    }

    duplicateLink() {
        const duplicatedLink = {
            ...this.link,
            id: undefined, // Will be generated
            title: `${this.link.title} (Copy)`,
            createdAt: new Date().toISOString()
        };
        
        window.dispatchEvent(new CustomEvent('link-add', {
            detail: { data: duplicatedLink }
        }));
        
        showSuccess('Link duplicated successfully');
    }

    deleteLink() {
        modalManager.confirm(
            `Are you sure you want to delete "${this.link.title || this.link.url}"?`,
            () => {
                window.dispatchEvent(new CustomEvent('link-delete', {
                    detail: { id: this.link.id }
                }));
                showSuccess('Link deleted successfully');
            },
            null,
            {
                title: 'Delete Link',
                confirmText: 'Delete',
                cancelText: 'Cancel'
            }
        );
    }

    trackLinkClick() {
        // Update click count or last accessed time
        const updatedLink = {
            ...this.link,
            clickCount: (this.link.clickCount || 0) + 1,
            lastAccessed: new Date().toISOString()
        };
        
        window.dispatchEvent(new CustomEvent('link-update', {
            detail: { id: this.link.id, data: updatedLink }
        }));
    }

    /**
     * Dropdown methods
     */
    
    toggleDropdown(dropdown) {
        const isVisible = dropdown.classList.contains('opacity-100');
        
        if (isVisible) {
            this.closeDropdown(dropdown);
        } else {
            this.openDropdown(dropdown);
        }
    }

    openDropdown(dropdown) {
        dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
        dropdown.classList.add('opacity-100', 'visible', 'scale-100');
    }

    closeDropdown(dropdown) {
        dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
        dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
    }

    /**
     * Utility methods
     */
    
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getDomainFromUrl(url) {
        try {
            return new URL(url).hostname.replace(/^www\./, '');
        } catch {
            return url;
        }
    }

    formatDate(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) return 'Today';
        if (diffDays === 2) return 'Yesterday';
        if (diffDays <= 7) return `${diffDays} days ago`;
        if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
        if (diffDays <= 365) return `${Math.ceil(diffDays / 30)} months ago`;
        
        return date.toLocaleDateString();
    }

    /**
     * Update card with new link data
     */
    update(newLinkData) {
        this.link = { ...this.link, ...newLinkData };
        
        // Re-render the card
        const oldCard = document.querySelector(`[data-link-id="${this.link.id}"]`);
        if (oldCard) {
            const newCard = this.render();
            oldCard.parentNode.replaceChild(newCard, oldCard);
        }
    }
}

// Utility function to create link card
function createLinkCard(link, options = {}) {
    const linkCard = new LinkCard(link, options);
    return linkCard.render();
}
