<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Preview Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-url {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #0088cc;
        }
        .test-url h3 {
            margin: 0 0 10px 0;
            color: #0088cc;
        }
        .url {
            font-family: monospace;
            background: #333;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .result {
            background: #1a3a1a;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .error {
            background: #3a1a1a;
            color: #ff6b6b;
        }
        button {
            background: #0088cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #006699;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔗 Telegram Preview Enhancement Test</h1>
        <p>This page tests the enhanced Telegram preview functionality with various URL formats.</p>
        
        <button onclick="testAllUrls()">Test All URLs</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="test-results">
            <!-- Test results will be populated here -->
        </div>
    </div>

    <!-- Include the Link Manager Pro scripts -->
    <script src="config.js"></script>
    <script src="scripts/platform-handlers.js"></script>
    <script src="scripts/preview-service.js"></script>

    <script>
        // Test URLs with expected results
        const testUrls = [
            {
                url: 'https://t.me/telegram',
                description: 'Official Telegram Channel',
                expectedType: 'channel'
            },
            {
                url: 'https://t.me/durov',
                description: 'Pavel Durov Channel',
                expectedType: 'channel'
            },
            {
                url: 'https://t.me/BotFather',
                description: 'BotFather Bot',
                expectedType: 'bot'
            },
            {
                url: 'https://t.me/telegram/123',
                description: 'Message in Telegram Channel',
                expectedType: 'message'
            },
            {
                url: 'https://t.me/c/1234567890/123',
                description: 'Private Channel Message',
                expectedType: 'private_channel_message'
            },
            {
                url: 'https://t.me/testbot?start=hello',
                description: 'Bot with Start Parameter',
                expectedType: 'bot_start'
            },
            {
                url: 'https://telegram.me/telegram',
                description: 'Alternative Domain',
                expectedType: 'channel'
            },
            {
                url: 'https://t.me/addstickers/AnimatedEmojies',
                description: 'Sticker Pack',
                expectedType: 'channel'
            }
        ];

        // Initialize preview service
        const previewService = new PreviewService();

        async function testAllUrls() {
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '<h2>🧪 Running Tests...</h2>';

            for (const testCase of testUrls) {
                await testSingleUrl(testCase);
            }

            // Add summary
            const successCount = document.querySelectorAll('.result:not(.error)').length;
            const totalCount = testUrls.length;
            
            const summary = document.createElement('div');
            summary.className = 'test-url';
            summary.innerHTML = `
                <h3>📊 Test Summary</h3>
                <p>Successful: ${successCount}/${totalCount} (${(successCount/totalCount*100).toFixed(1)}%)</p>
            `;
            resultsContainer.appendChild(summary);
        }

        async function testSingleUrl(testCase) {
            const resultsContainer = document.getElementById('test-results');
            
            const testDiv = document.createElement('div');
            testDiv.className = 'test-url';
            
            testDiv.innerHTML = `
                <h3>${testCase.description}</h3>
                <div class="url">${testCase.url}</div>
                <div class="result">Testing...</div>
            `;
            
            resultsContainer.appendChild(testDiv);
            const resultDiv = testDiv.querySelector('.result');

            try {
                const startTime = Date.now();
                const preview = await previewService.getPreview(testCase.url, false);
                const duration = Date.now() - startTime;

                // Check if the result matches expectations
                const typeMatch = preview.entityType === testCase.expectedType || 
                                preview.telegramType === testCase.expectedType;

                resultDiv.innerHTML = `
                    <strong>✅ Success (${duration}ms)</strong><br>
                    <strong>Title:</strong> ${preview.title || 'N/A'}<br>
                    <strong>Description:</strong> ${preview.description || 'N/A'}<br>
                    <strong>Entity Type:</strong> ${preview.entityType || 'N/A'}<br>
                    <strong>Username:</strong> ${preview.username || 'N/A'}<br>
                    ${preview.messageId ? `<strong>Message ID:</strong> ${preview.messageId}<br>` : ''}
                    ${preview.startParam ? `<strong>Start Param:</strong> ${preview.startParam}<br>` : ''}
                    <strong>Type Match:</strong> ${typeMatch ? '✅' : '❌'} (Expected: ${testCase.expectedType})
                `;

                if (!typeMatch) {
                    resultDiv.style.background = '#3a2a1a';
                    resultDiv.style.color = '#ffcc00';
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ Error:</strong> ${error.message}
                `;
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(testAllUrls, 1000);
        });
    </script>
</body>
</html>
