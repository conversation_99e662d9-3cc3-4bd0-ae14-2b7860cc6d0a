// Storage Management System
class StorageManager {
    constructor() {
        this.storageType = getConfig('STORAGE.TYPE', 'localStorage');
        this.keys = getConfig('STORAGE.KEYS');
        this.autoBackupInterval = getConfig('STORAGE.AUTO_BACKUP_INTERVAL', 30) * 60 * 1000; // Convert to ms
        this.maxBackups = getConfig('STORAGE.MAX_BACKUPS', 10);
        
        this.initializeStorage();
        this.setupAutoBackup();
    }

    /**
     * Initialize storage system
     */
    initializeStorage() {
        // Check if this is the first run
        if (!this.get(this.keys.SETTINGS)) {
            this.initializeDefaultData();
        }
        
        // Migrate data if needed
        this.migrateData();
    }

    /**
     * Initialize default data for first-time users
     */
    initializeDefaultData() {
        const defaultSettings = {
            theme: getConfig('UI.THEME', 'dark'),
            viewMode: getConfig('UI.DEFAULT_VIEW', 'grid'),
            autoPreview: getConfig('PREVIEW.AUTO_FETCH', true),
            notifications: true,
            version: '1.0.0',
            firstRun: true,
            createdAt: new Date().toISOString()
        };

        const defaultCategories = getConfig('CATEGORIES.DEFAULT_CATEGORIES', []);

        this.set(this.keys.SETTINGS, defaultSettings);
        this.set(this.keys.CATEGORIES, defaultCategories);
        this.set(this.keys.LINKS, []);
        
        console.log('Initialized default data for new user');
    }

    /**
     * Get data from storage
     * @param {string} key - Storage key
     * @param {*} defaultValue - Default value if key doesn't exist
     */
    get(key, defaultValue = null) {
        try {
            switch (this.storageType) {
                case 'localStorage':
                    const item = localStorage.getItem(key);
                    return item ? JSON.parse(item) : defaultValue;
                
                case 'sessionStorage':
                    const sessionItem = sessionStorage.getItem(key);
                    return sessionItem ? JSON.parse(sessionItem) : defaultValue;
                
                default:
                    console.warn(`Unsupported storage type: ${this.storageType}`);
                    return defaultValue;
            }
        } catch (error) {
            console.error(`Error reading from storage (${key}):`, error);
            return defaultValue;
        }
    }

    /**
     * Set data in storage
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     */
    set(key, value) {
        try {
            const serializedValue = JSON.stringify(value);
            
            switch (this.storageType) {
                case 'localStorage':
                    localStorage.setItem(key, serializedValue);
                    break;
                
                case 'sessionStorage':
                    sessionStorage.setItem(key, serializedValue);
                    break;
                
                default:
                    console.warn(`Unsupported storage type: ${this.storageType}`);
                    return false;
            }
            
            // Dispatch storage change event
            this.dispatchStorageEvent(key, value);
            return true;
        } catch (error) {
            console.error(`Error writing to storage (${key}):`, error);
            
            // Handle quota exceeded error
            if (error.name === 'QuotaExceededError') {
                this.handleQuotaExceeded();
            }
            
            return false;
        }
    }

    /**
     * Remove data from storage
     * @param {string} key - Storage key
     */
    remove(key) {
        try {
            switch (this.storageType) {
                case 'localStorage':
                    localStorage.removeItem(key);
                    break;
                
                case 'sessionStorage':
                    sessionStorage.removeItem(key);
                    break;
            }
            
            this.dispatchStorageEvent(key, null);
            return true;
        } catch (error) {
            console.error(`Error removing from storage (${key}):`, error);
            return false;
        }
    }

    /**
     * Clear all app data
     */
    clear() {
        try {
            Object.values(this.keys).forEach(key => {
                this.remove(key);
            });
            return true;
        } catch (error) {
            console.error('Error clearing storage:', error);
            return false;
        }
    }

    /**
     * Get storage usage information
     */
    getStorageInfo() {
        if (this.storageType !== 'localStorage') {
            return { used: 0, available: 0, total: 0 };
        }

        try {
            let used = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    used += localStorage[key].length + key.length;
                }
            }

            // Estimate total available storage (usually 5-10MB)
            const total = 10 * 1024 * 1024; // 10MB estimate
            const available = total - used;

            return {
                used: used,
                available: available,
                total: total,
                usedMB: (used / (1024 * 1024)).toFixed(2),
                availableMB: (available / (1024 * 1024)).toFixed(2),
                totalMB: (total / (1024 * 1024)).toFixed(2),
                usagePercent: ((used / total) * 100).toFixed(1)
            };
        } catch (error) {
            console.error('Error getting storage info:', error);
            return { used: 0, available: 0, total: 0 };
        }
    }

    /**
     * Handle storage quota exceeded
     */
    handleQuotaExceeded() {
        console.warn('Storage quota exceeded, attempting cleanup...');
        
        // Try to free up space by removing old backups
        this.cleanupOldBackups();
        
        // Show warning to user
        showWarning('Storage is almost full. Consider exporting your data or clearing old items.', 0, {
            action: () => window.dispatchEvent(new CustomEvent('show-storage-settings')),
            actionText: 'Manage Storage'
        });
    }

    /**
     * Create backup of current data
     */
    createBackup() {
        const backup = {
            timestamp: new Date().toISOString(),
            version: this.get(this.keys.SETTINGS)?.version || '1.0.0',
            data: {
                links: this.get(this.keys.LINKS, []),
                categories: this.get(this.keys.CATEGORIES, []),
                settings: this.get(this.keys.SETTINGS, {})
            }
        };

        const backups = this.get(this.keys.BACKUP, []);
        backups.unshift(backup);

        // Keep only the most recent backups
        if (backups.length > this.maxBackups) {
            backups.splice(this.maxBackups);
        }

        this.set(this.keys.BACKUP, backups);
        console.log('Backup created successfully');
        
        return backup;
    }

    /**
     * Restore from backup
     * @param {number} backupIndex - Index of backup to restore
     */
    restoreFromBackup(backupIndex = 0) {
        const backups = this.get(this.keys.BACKUP, []);
        
        if (backupIndex >= backups.length) {
            throw new Error('Backup not found');
        }

        const backup = backups[backupIndex];
        
        // Restore data
        this.set(this.keys.LINKS, backup.data.links);
        this.set(this.keys.CATEGORIES, backup.data.categories);
        this.set(this.keys.SETTINGS, backup.data.settings);

        console.log('Data restored from backup:', backup.timestamp);
        showSuccess('Data restored successfully from backup');
        
        // Reload the app
        window.location.reload();
    }

    /**
     * Get list of available backups
     */
    getBackups() {
        return this.get(this.keys.BACKUP, []);
    }

    /**
     * Clean up old backups
     */
    cleanupOldBackups() {
        const backups = this.get(this.keys.BACKUP, []);
        
        if (backups.length > this.maxBackups) {
            const trimmedBackups = backups.slice(0, this.maxBackups);
            this.set(this.keys.BACKUP, trimmedBackups);
            console.log(`Cleaned up ${backups.length - this.maxBackups} old backups`);
        }
    }

    /**
     * Setup automatic backup
     */
    setupAutoBackup() {
        if (this.autoBackupInterval > 0) {
            setInterval(() => {
                this.createBackup();
            }, this.autoBackupInterval);
            
            console.log(`Auto-backup enabled (every ${this.autoBackupInterval / 60000} minutes)`);
        }
    }

    /**
     * Export all data
     */
    exportData() {
        return {
            exportDate: new Date().toISOString(),
            version: this.get(this.keys.SETTINGS)?.version || '1.0.0',
            data: {
                links: this.get(this.keys.LINKS, []),
                categories: this.get(this.keys.CATEGORIES, []),
                settings: this.get(this.keys.SETTINGS, {})
            }
        };
    }

    /**
     * Import data
     * @param {Object} importData - Data to import
     * @param {boolean} merge - Whether to merge with existing data
     */
    importData(importData, merge = false) {
        try {
            if (!importData.data) {
                throw new Error('Invalid import data format');
            }

            // Create backup before import
            this.createBackup();

            if (merge) {
                // Merge with existing data
                const existingLinks = this.get(this.keys.LINKS, []);
                const existingCategories = this.get(this.keys.CATEGORIES, []);
                
                // Merge links (avoid duplicates by URL)
                const mergedLinks = [...existingLinks];
                importData.data.links.forEach(link => {
                    if (!existingLinks.find(existing => existing.url === link.url)) {
                        mergedLinks.push({ ...link, id: this.generateId() });
                    }
                });

                // Merge categories (avoid duplicates by name)
                const mergedCategories = [...existingCategories];
                importData.data.categories.forEach(category => {
                    if (!existingCategories.find(existing => existing.name === category.name)) {
                        mergedCategories.push({ ...category, id: this.generateId() });
                    }
                });

                this.set(this.keys.LINKS, mergedLinks);
                this.set(this.keys.CATEGORIES, mergedCategories);
            } else {
                // Replace existing data
                this.set(this.keys.LINKS, importData.data.links || []);
                this.set(this.keys.CATEGORIES, importData.data.categories || []);
                
                // Merge settings (keep user preferences)
                const currentSettings = this.get(this.keys.SETTINGS, {});
                const importedSettings = importData.data.settings || {};
                this.set(this.keys.SETTINGS, { ...currentSettings, ...importedSettings });
            }

            console.log('Data imported successfully');
            showSuccess('Data imported successfully');
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            showError('Failed to import data: ' + error.message);
            return false;
        }
    }

    /**
     * Migrate data between versions
     */
    migrateData() {
        const settings = this.get(this.keys.SETTINGS, {});
        const currentVersion = settings.version || '1.0.0';
        
        // Add migration logic here for future versions
        console.log(`Data version: ${currentVersion}`);
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Dispatch storage change event
     */
    dispatchStorageEvent(key, value) {
        window.dispatchEvent(new CustomEvent('storage-change', {
            detail: { key, value }
        }));
    }
}

// Create global instance
const storageManager = new StorageManager();
