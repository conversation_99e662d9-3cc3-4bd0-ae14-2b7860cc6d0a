// Modal System for Link Manager Pro
class ModalManager {
    constructor() {
        this.container = document.getElementById('modalContainer');
        this.activeModal = null;
        this.modalStack = [];
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Close modal on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.close();
            }
        });
    }

    /**
     * Show a modal
     * @param {Object} options - Modal configuration
     */
    show(options = {}) {
        const modal = this.createModal(options);
        this.container.appendChild(modal);
        
        // Store reference
        this.modalStack.push(this.activeModal);
        this.activeModal = modal;

        // Trigger animation
        requestAnimationFrame(() => {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        });

        return modal;
    }

    /**
     * Close the active modal
     */
    close() {
        if (!this.activeModal) return;

        const modal = this.activeModal;
        modal.classList.remove('show');

        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
            
            // Restore previous modal or unlock body scroll
            this.activeModal = this.modalStack.pop() || null;
            if (!this.activeModal) {
                document.body.style.overflow = '';
            }
        }, 300);
    }

    /**
     * Create modal element
     * @private
     */
    createModal(options) {
        const {
            title = 'Modal',
            content = '',
            size = 'md',
            closable = true,
            actions = [],
            className = '',
            onClose = null
        } = options;

        const modal = document.createElement('div');
        modal.className = `modal-overlay fixed inset-0 z-50 flex items-center justify-center p-4 opacity-0 transition-opacity duration-300 ${className}`;

        const sizeClasses = {
            sm: 'max-w-md',
            md: 'max-w-lg',
            lg: 'max-w-2xl',
            xl: 'max-w-4xl',
            full: 'max-w-full mx-4'
        };

        modal.innerHTML = `
            <div class="modal-content w-full ${sizeClasses[size]} rounded-2xl p-6 transform scale-95 transition-transform duration-300">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">${this.escapeHtml(title)}</h2>
                    ${closable ? `
                        <button class="modal-close glass-button p-2 rounded-lg hover:bg-white/20 transition-colors">
                            <i class="fas fa-times text-white"></i>
                        </button>
                    ` : ''}
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${actions.length > 0 ? `
                    <div class="modal-actions flex justify-end space-x-3 mt-6 pt-6 border-t border-white/20">
                        ${actions.map(action => `
                            <button class="modal-action-btn ${action.className || 'glass-button'} px-6 py-2 rounded-lg font-medium transition-all"
                                    data-action="${action.action || ''}"
                                    ${action.disabled ? 'disabled' : ''}>
                                ${action.icon ? `<i class="${action.icon} mr-2"></i>` : ''}
                                ${this.escapeHtml(action.text)}
                            </button>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        // Add event listeners
        this.setupModalEventListeners(modal, onClose);

        return modal;
    }

    /**
     * Setup event listeners for modal
     * @private
     */
    setupModalEventListeners(modal, onClose) {
        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                if (onClose) onClose();
                this.close();
            }
        });

        // Close button
        const closeBtn = modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (onClose) onClose();
                this.close();
            });
        }

        // Action buttons
        const actionBtns = modal.querySelectorAll('.modal-action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.getAttribute('data-action');
                if (action) {
                    const event = new CustomEvent('modal-action', {
                        detail: { action, modal, button: btn }
                    });
                    modal.dispatchEvent(event);
                }
            });
        });

        // Animation classes
        requestAnimationFrame(() => {
            modal.classList.add('opacity-100');
            modal.querySelector('.modal-content').classList.add('scale-100');
        });
    }

    /**
     * Escape HTML
     * @private
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Predefined modal types
    
    /**
     * Show confirmation modal
     */
    confirm(message, onConfirm, onCancel, options = {}) {
        return this.show({
            title: options.title || 'Confirm Action',
            content: `<p class="text-gray-300">${this.escapeHtml(message)}</p>`,
            size: options.size || 'sm',
            actions: [
                {
                    text: options.cancelText || 'Cancel',
                    className: 'glass-button text-white',
                    action: 'cancel'
                },
                {
                    text: options.confirmText || 'Confirm',
                    className: 'glass-button-primary text-white',
                    action: 'confirm'
                }
            ],
            onClose: onCancel
        }).addEventListener('modal-action', (e) => {
            if (e.detail.action === 'confirm') {
                if (onConfirm) onConfirm();
                this.close();
            } else if (e.detail.action === 'cancel') {
                if (onCancel) onCancel();
                this.close();
            }
        });
    }

    /**
     * Show link form modal
     */
    showLinkForm(link = null, onSave, onCancel) {
        const isEdit = !!link;
        const title = isEdit ? 'Edit Link' : 'Add New Link';
        
        const content = `
            <form id="linkForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">URL *</label>
                    <input type="url" id="linkUrl" required
                           class="form-input w-full px-4 py-3 rounded-xl"
                           placeholder="https://example.com"
                           value="${link?.url || ''}">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                    <input type="text" id="linkTitle"
                           class="form-input w-full px-4 py-3 rounded-xl"
                           placeholder="Link title (auto-generated if empty)"
                           value="${link?.title || ''}">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea id="linkDescription" rows="3"
                              class="form-input w-full px-4 py-3 rounded-xl resize-none"
                              placeholder="Optional description">${link?.description || ''}</textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select id="linkCategory" class="form-input w-full px-4 py-3 rounded-xl">
                        <option value="">Select category</option>
                        <!-- Categories will be populated by JavaScript -->
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
                    <input type="text" id="linkTags"
                           class="form-input w-full px-4 py-3 rounded-xl"
                           placeholder="Separate tags with commas"
                           value="${link?.tags?.join(', ') || ''}">
                    <p class="text-xs text-gray-400 mt-1">Separate multiple tags with commas</p>
                </div>
                
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="linkFavorite" class="rounded"
                           ${link?.favorite ? 'checked' : ''}>
                    <label for="linkFavorite" class="text-sm text-gray-300">Mark as favorite</label>
                </div>
                
                ${isEdit ? `<input type="hidden" id="linkId" value="${link.id}">` : ''}
            </form>
        `;

        const modal = this.show({
            title,
            content,
            size: 'md',
            actions: [
                {
                    text: 'Cancel',
                    className: 'glass-button text-white',
                    action: 'cancel'
                },
                {
                    text: isEdit ? 'Update Link' : 'Add Link',
                    className: 'glass-button-primary text-white',
                    action: 'save',
                    icon: 'fas fa-save'
                }
            ]
        });

        // Populate categories
        this.populateCategories(modal);

        // Handle form submission
        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'save') {
                e.preventDefault();
                this.handleLinkFormSubmit(modal, onSave, isEdit);
            } else if (e.detail.action === 'cancel') {
                if (onCancel) onCancel();
                this.close();
            }
        });

        // Auto-fetch title when URL changes
        const urlInput = modal.querySelector('#linkUrl');
        let fetchTimeout;
        urlInput.addEventListener('input', () => {
            clearTimeout(fetchTimeout);
            fetchTimeout = setTimeout(() => {
                this.autoFetchLinkData(modal, urlInput.value);
            }, 1000);
        });

        return modal;
    }

    /**
     * Populate categories in form
     * @private
     */
    populateCategories(modal) {
        const select = modal.querySelector('#linkCategory');
        const categories = categoryManager.getCategories();
        
        categories.forEach(category => {
            if (category.id !== 'all') {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            }
        });
    }

    /**
     * Handle link form submission
     * @private
     */
    handleLinkFormSubmit(modal, onSave, isEdit) {
        const form = modal.querySelector('#linkForm');
        const formData = new FormData(form);
        
        const linkData = {
            url: modal.querySelector('#linkUrl').value.trim(),
            title: modal.querySelector('#linkTitle').value.trim(),
            description: modal.querySelector('#linkDescription').value.trim(),
            category: modal.querySelector('#linkCategory').value,
            tags: modal.querySelector('#linkTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            favorite: modal.querySelector('#linkFavorite').checked
        };

        if (isEdit) {
            linkData.id = modal.querySelector('#linkId').value;
        }

        // Validate
        if (!linkData.url) {
            showError('URL is required');
            return;
        }

        if (!CONFIG.VALIDATION.URL_PATTERN.test(linkData.url)) {
            showError('Please enter a valid URL');
            return;
        }

        if (onSave) {
            onSave(linkData);
        }
        
        this.close();
    }

    /**
     * Auto-fetch link data from URL
     * @private
     */
    async autoFetchLinkData(modal, url) {
        if (!url || !CONFIG.VALIDATION.URL_PATTERN.test(url)) return;

        const titleInput = modal.querySelector('#linkTitle');
        const descInput = modal.querySelector('#linkDescription');

        // Only auto-fetch if title is empty
        if (titleInput.value.trim()) return;

        try {
            const preview = await previewService.getPreview(url);
            if (preview) {
                if (!titleInput.value.trim() && preview.title) {
                    titleInput.value = preview.title;
                }
                if (!descInput.value.trim() && preview.description) {
                    descInput.value = preview.description;
                }
            }
        } catch (error) {
            console.warn('Failed to auto-fetch link data:', error);
        }
    }
}

// Create global instance
const modalManager = new ModalManager();
