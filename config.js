// Configuration file for Link Manager Pro
const CONFIG = {
    // API Configuration
    API: {
        // Primary preview services
        MICROLINK_BASE_URL: 'https://api.microlink.io',
        LINKPREVIEW_BASE_URL: 'https://api.linkpreview.net',

        // Platform-specific APIs
        YOUTUBE_API_BASE: 'https://www.googleapis.com/youtube/v3',
        TWITTER_API_BASE: 'https://api.twitter.com/2',
        REDDIT_API_BASE: 'https://www.reddit.com',

        // API Keys (add your own for enhanced features)
        MICROLINK_API_KEY: '',
        LINKPREVIEW_API_KEY: '',
        YOUTUBE_API_KEY: '', // For enhanced YouTube metadata
        TWITTER_BEARER_TOKEN: '', // For Twitter API v2

        // Request configuration
        REQUEST_TIMEOUT: 15000,
        MAX_RETRIES: 3,
        RETRY_DELAY: 1000,
        CONCURRENT_REQUESTS: 3,

        // Rate limiting
        RATE_LIMITS: {
            MICROLINK: { requests: 100, window: 86400000 }, // 100/day
            YOUTUBE: { requests: 10000, window: 86400000 }, // 10k/day
            TWITTER: { requests: 300, window: 900000 }, // 300/15min
            REDDIT: { requests: 60, window: 60000 } // 60/min
        }
    },

    // Storage Configuration
    STORAGE: {
        // Storage type: 'localStorage', 'indexedDB', 'cloud'
        TYPE: 'localStorage',
        
        // Storage keys
        KEYS: {
            LINKS: 'linkManager_links',
            CATEGORIES: 'linkManager_categories',
            SETTINGS: 'linkManager_settings',
            BACKUP: 'linkManager_backup'
        },
        
        // Auto-backup interval (in minutes)
        AUTO_BACKUP_INTERVAL: 30,
        
        // Maximum number of backups to keep
        MAX_BACKUPS: 10
    },

    // UI Configuration
    UI: {
        // Default theme
        THEME: 'dark',
        
        // Animation duration (in milliseconds)
        ANIMATION_DURATION: 300,
        
        // Toast notification duration (in milliseconds)
        TOAST_DURATION: 4000,
        
        // Number of links per page for pagination
        LINKS_PER_PAGE: 12,
        
        // Default view mode: 'grid', 'list'
        DEFAULT_VIEW: 'grid',
        
        // Enable/disable features
        FEATURES: {
            LINK_PREVIEWS: true,
            CATEGORIES: true,
            SEARCH: true,
            EXPORT: true,
            IMPORT: true,
            BULK_ACTIONS: true,
            KEYBOARD_SHORTCUTS: true,
            DRAG_DROP: true,
            SHARING: true,
            ANALYTICS: true,
            RICH_TEXT: true,
            CLOUD_SYNC: false
        }
    },

    // Export Configuration
    EXPORT: {
        // Available export formats
        FORMATS: ['json', 'csv', 'markdown', 'html', 'pdf'],

        // Default export format
        DEFAULT_FORMAT: 'json',

        // Include metadata in exports
        INCLUDE_METADATA: true,

        // PDF export options
        PDF_OPTIONS: {
            format: 'A4',
            margin: '1in',
            printBackground: true
        }
    },

    // Sharing Configuration
    SHARING: {
        // Available sharing platforms
        PLATFORMS: ['telegram', 'whatsapp', 'email', 'clipboard', 'twitter'],

        // Password protection for shared data
        PASSWORD_PROTECTION: true,

        // Share formats
        FORMATS: ['json', 'html', 'markdown'],

        // Default share format
        DEFAULT_FORMAT: 'html'
    },

    // Link Preview Configuration
    PREVIEW: {
        // Enable automatic preview fetching
        AUTO_FETCH: true,

        // Cache configuration
        CACHE_DURATION: 24, // hours
        CACHE_REFRESH_THRESHOLD: 7, // days for dynamic content
        MAX_CACHE_SIZE: 1000, // number of cached previews

        // Platform-specific settings
        PLATFORMS: {
            YOUTUBE: {
                enabled: true,
                fetchMetrics: true,
                thumbnailQuality: 'maxresdefault',
                includeTranscript: false
            },
            TWITTER: {
                enabled: true,
                fetchMetrics: true,
                includeThread: true,
                expandUrls: true
            },
            INSTAGRAM: {
                enabled: true,
                fetchMetrics: false, // Limited by API
                thumbnailSize: 'medium'
            },
            TIKTOK: {
                enabled: true,
                fetchMetrics: true,
                videoPreview: true
            },
            FACEBOOK: {
                enabled: true,
                fetchMetrics: false, // Privacy restrictions
                openGraphOnly: true
            },
            LINKEDIN: {
                enabled: true,
                fetchMetrics: false,
                professionalMode: true
            },
            REDDIT: {
                enabled: true,
                fetchMetrics: true,
                includeComments: false
            },
            TELEGRAM: {
                enabled: true,
                fetchMetrics: false,
                channelPreviews: true
            }
        },

        // Fallback configuration
        FALLBACK_IMAGE: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+CjxwYXRoIGQ9Ik0xMDAgNzBMMTMwIDEwMEgxMTBWMTMwSDkwVjEwMEg3MEwxMDAgNzBaIiBmaWxsPSIjNjY2Ii8+Cjwvc3ZnPgo=',

        // Image optimization
        IMAGE_QUALITY: 80,
        MAX_IMAGE_SIZE: 500, // KB
        THUMBNAIL_SIZES: {
            small: 150,
            medium: 300,
            large: 600
        },

        // Performance settings
        BATCH_SIZE: 5,
        CONCURRENT_FETCHES: 3,
        TIMEOUT_PER_PLATFORM: 8000,

        // Metadata preferences
        METADATA_FIELDS: {
            title: true,
            description: true,
            image: true,
            author: true,
            publishDate: true,
            metrics: true,
            duration: true,
            platform: true
        }
    },

    // Categories Configuration
    CATEGORIES: {
        // Default categories
        DEFAULT_CATEGORIES: [
            { id: 'all', name: 'All Links', icon: 'fas fa-globe', color: '#6366f1' },
            { id: 'work', name: 'Work', icon: 'fas fa-briefcase', color: '#059669' },
            { id: 'personal', name: 'Personal', icon: 'fas fa-user', color: '#dc2626' },
            { id: 'learning', name: 'Learning', icon: 'fas fa-graduation-cap', color: '#7c3aed' },
            { id: 'entertainment', name: 'Entertainment', icon: 'fas fa-play', color: '#ea580c' },
            { id: 'tools', name: 'Tools', icon: 'fas fa-tools', color: '#0891b2' },
            { id: 'favorites', name: 'Favorites', icon: 'fas fa-star', color: '#ca8a04' }
        ],
        
        // Maximum number of categories
        MAX_CATEGORIES: 20,
        
        // Allow custom category colors
        CUSTOM_COLORS: true
    },

    // Search Configuration
    SEARCH: {
        // Minimum characters to trigger search
        MIN_SEARCH_LENGTH: 2,
        
        // Search delay (debounce) in milliseconds
        SEARCH_DELAY: 300,
        
        // Search fields
        SEARCH_FIELDS: ['title', 'description', 'url', 'tags'],
        
        // Enable fuzzy search
        FUZZY_SEARCH: true,
        
        // Search result highlighting
        HIGHLIGHT_RESULTS: true
    },

    // Keyboard Shortcuts
    SHORTCUTS: {
        ADD_LINK: 'ctrl+n',
        SEARCH: 'ctrl+f',
        EXPORT: 'ctrl+e',
        SETTINGS: 'ctrl+comma',
        DELETE_SELECTED: 'delete',
        SELECT_ALL: 'ctrl+a',
        ESCAPE: 'escape'
    },

    // Validation Rules
    VALIDATION: {
        URL_PATTERN: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
        TITLE_MAX_LENGTH: 100,
        DESCRIPTION_MAX_LENGTH: 500,
        TAG_MAX_LENGTH: 30,
        MAX_TAGS_PER_LINK: 10
    },

    // Performance Configuration
    PERFORMANCE: {
        // Virtual scrolling threshold
        VIRTUAL_SCROLL_THRESHOLD: 100,
        
        // Image lazy loading
        LAZY_LOADING: true,
        
        // Debounce delays
        DEBOUNCE: {
            SEARCH: 300,
            RESIZE: 250,
            SCROLL: 100
        }
    },

    // Development Configuration
    DEV: {
        // Enable debug mode
        DEBUG: false,
        
        // Enable performance monitoring
        PERFORMANCE_MONITORING: false,
        
        // Mock API responses for development
        MOCK_API: false,
        
        // Console logging level: 'error', 'warn', 'info', 'debug'
        LOG_LEVEL: 'info'
    }
};

// Utility function to get nested config values
function getConfig(path, defaultValue = null) {
    return path.split('.').reduce((obj, key) => obj?.[key], CONFIG) ?? defaultValue;
}

// Utility function to set nested config values
function setConfig(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, CONFIG);
    target[lastKey] = value;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, getConfig, setConfig };
}
