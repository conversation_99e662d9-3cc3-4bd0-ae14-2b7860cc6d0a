# Link Manager Pro

A modern, responsive, single-page Link Manager web application built with vanilla JavaScript, featuring a beautiful glassmorphism design and comprehensive link organization capabilities.

## ✨ Features

### 🌐 Advanced Link Preview System
- **Platform-Specific Handlers** - Specialized support for YouTube, Twitter, Instagram, TikTok, Reddit, LinkedIn, Facebook, Telegram
- **Rich Metadata Extraction** - Video duration, view counts, engagement metrics, author info, publish dates
- **Multiple API Integration** - YouTube Data API, Twitter API v2, Reddit JSON API, oEmbed services
- **Smart Fallback System** - Graceful degradation from premium APIs to basic Open Graph parsing
- **Intelligent Caching** - Platform-aware cache duration with smart refresh for dynamic content
- **Rate Limit Management** - Built-in rate limiting with quota tracking for all services

### 💎 Glassmorphism UI
- **Glass Cards** - Beautiful glass-like interface with blur and soft shadows
- **Floating Action Button** - Quick add button for mobile devices
- **Responsive Grid & List Views** - Toggle between grid and list layouts
- **Dark/Light Mode Support** - Customizable theme options
- **Smooth Animations** - Fluid transitions and micro-interactions

### 🗂️ Advanced Organization
- **Custom Categories** - Create, edit, and delete custom categories with icons
- **Multi-Category Support** - Assign links to multiple categories
- **Smart Tags System** - Flexible tagging with auto-suggestions
- **Drag & Drop Reordering** - Visual reordering within and across groups
- **Favorites System** - Star important links for quick access

### 🛠️ Powerful Link Editing
- **Inline Editing** - Edit title, URL, description, and image directly
- **Rich Text Support** - Basic formatting for descriptions
- **Custom Tag Management** - Add, edit, and organize tags
- **Visual Card Reordering** - Drag and drop to reorganize links
- **Bulk Operations** - Select and manage multiple links at once

### 💾 Comprehensive Data Management
- **Local Storage** - All data stored securely in your browser
- **Cloud Sync Options** - Optional Firebase, Supabase, or GitHub Gist sync
- **Multi-Format Export/Import**:
  - **JSON** - Complete backup with metadata
  - **CSV** - Spreadsheet-compatible format
  - **Markdown** - Documentation-friendly format
  - **HTML** - Shareable bookmark page
  - **PDF** - Printable snapshot (optional)

### 📤 Advanced Sharing
- **Multiple Share Formats** - JSON, HTML, or Markdown
- **Platform Integration** - Share via Telegram, WhatsApp, Email, Twitter
- **One-Click Clipboard** - Copy formatted data instantly
- **Password Protection** - Secure shared data with passwords
- **Custom Share Views** - Generate beautiful HTML pages

### ⚙️ Extensive Customization
- **Theme Customization** - Adjust colors, blur levels, and font sizes
- **View Mode Toggle** - Switch between grid and list layouts
- **Preview Settings** - Control automatic preview fetching
- **Export Preferences** - Set default formats and options
- **Backup Reminders** - Configurable backup notifications
- **Performance Tuning** - Optimize for your device

### 🔔 Smart UX & Feedback
- **Toast Notifications** - Real-time feedback for every action
- **Fallback Handling** - Graceful degradation on preview failures
- **Multi-Filter Search** - Search by text, domain, tags, or category
- **Usage Analytics** - Track most viewed links and usage patterns
- **Keyboard Shortcuts** - Efficient navigation for power users

## 🚀 Quick Start

1. **Clone or Download** this repository
2. **Open** `index.html` in your web browser
3. **Start Adding Links** - Click "Add New Link" to get started

No installation or build process required! The app runs entirely in your browser.

## 📱 Usage

### Adding Links
1. Click the "Add New Link" button
2. Enter the URL (required)
3. Optionally add title, description, category, and tags
4. Click "Add Link" to save

### Organizing Links
- **Categories**: Create custom categories to group related links
- **Tags**: Add multiple tags to links for flexible organization
- **Favorites**: Star important links for quick access

### Searching & Filtering
- Use the search bar to find links by title, description, URL, or tags
- Click on categories in the sidebar to filter links
- Use the "Favorites" category to see only starred links

### Exporting Data
1. Click the export button in the header
2. Choose your preferred format (JSON, CSV, Markdown, HTML)
3. Select what to include (links, categories, settings)
4. Download your data

## 🛠️ Technical Details

### Architecture
- **Vanilla JavaScript** - No frameworks, pure JavaScript for maximum compatibility
- **Modular Design** - Clean separation of concerns with dedicated modules
- **Event-Driven** - Reactive architecture using custom events
- **Progressive Enhancement** - Works without JavaScript for basic functionality

### Browser Compatibility
- **Modern Browsers** - Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile Browsers** - iOS Safari 13+, Chrome Mobile 80+
- **Features Used** - ES6+, Fetch API, Local Storage, CSS Grid/Flexbox

### Performance
- **Lazy Loading** - Images and previews loaded on demand
- **Debounced Search** - Optimized search with input debouncing
- **Virtual Scrolling** - Efficient rendering for large link collections
- **Caching** - Smart caching of link previews and metadata

## 📂 Project Structure

```
link-manager-pro/
├── index.html              # Main application entry point
├── styles/
│   └── styles.css          # TailwindCSS + custom glassmorphism styles
├── scripts/
│   ├── app.js              # Main application logic
│   ├── preview-service.js  # Link preview fetching service
│   ├── storage.js          # Data storage management
│   ├── export.js           # Export/import functionality
│   ├── categories.js       # Category management
│   └── settings.js         # Settings and preferences
├── components/
│   ├── linkCard.js         # Link card component
│   ├── modal.js            # Modal system
│   ├── toast.js            # Notification system
│   └── sidebar.js          # Sidebar navigation
├── assets/
│   └── icons/              # SVG icons and assets
├── data/
│   └── sample.json         # Sample export data
├── config.js               # Configuration and constants
└── README.md               # This file
```

## ⚙️ Configuration

### API Configuration
The app uses external APIs for link previews. You can configure API keys in `config.js`:

```javascript
const CONFIG = {
    API: {
        MICROLINK_API_KEY: 'your-api-key-here',
        LINKPREVIEW_API_KEY: 'your-api-key-here'
    }
};
```

### Customization Options
- **Themes**: Modify CSS variables for custom color schemes
- **Categories**: Add default categories in the config
- **Features**: Enable/disable features via configuration
- **Performance**: Adjust caching and loading behavior

## 🔧 Development

### Local Development
1. Clone the repository
2. Open `index.html` in a web browser
3. Make changes to the code
4. Refresh the browser to see changes

### Adding Features
1. Create new modules in the appropriate directory
2. Follow the existing code patterns and conventions
3. Update the configuration if needed
4. Test across different browsers and devices

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📊 Data Format

### Link Object
```javascript
{
    "id": "unique-id",
    "title": "Link Title",
    "url": "https://example.com",
    "description": "Link description",
    "category": "category-id",
    "tags": ["tag1", "tag2"],
    "favorite": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "clickCount": 0,
    "preview": {
        "title": "Page Title",
        "description": "Page Description",
        "image": "https://example.com/image.jpg",
        "siteName": "Site Name",
        "favicon": "https://example.com/favicon.ico"
    }
}
```

### Export Format
The app exports data in a structured JSON format that includes:
- Links with full metadata
- Categories and their configurations
- User settings and preferences
- Export metadata and version information

## 🔒 Privacy & Security

- **Local Storage Only** - All data stays in your browser
- **No Tracking** - No analytics or tracking scripts
- **HTTPS Required** - Secure connections for external API calls
- **Data Control** - Full control over your data with export/import

## 🆘 Troubleshooting

### Common Issues

**Links not loading previews:**
- Check your internet connection
- Verify API configuration in `config.js`
- Some sites may block preview generation

**Data not saving:**
- Ensure browser supports Local Storage
- Check available storage space
- Try clearing browser cache

**Performance issues:**
- Reduce the number of links displayed per page
- Disable animations in settings
- Clear preview cache

### Browser Support
If you experience issues:
1. Update to the latest browser version
2. Enable JavaScript
3. Clear browser cache and cookies
4. Check browser console for errors

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

## 📞 Support

If you encounter any issues or have questions:
1. Check the troubleshooting section above
2. Search existing issues on GitHub
3. Create a new issue with detailed information
4. Include browser version and error messages

---

**Link Manager Pro** - Organize your digital world with style! 🌟
