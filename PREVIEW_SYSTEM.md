# Enhanced Preview System Documentation

## Overview

The Link Manager Pro now features a comprehensive preview system with specialized handlers for major social media platforms and video services. This system provides rich metadata extraction, platform-specific features, and intelligent fallback mechanisms.

## Supported Platforms

### 🎥 Video Platforms

#### YouTube (`youtube.com`, `youtu.be`)
- **Enhanced Features**: Video duration, view counts, like counts, comment counts
- **API Integration**: YouTube Data API v3 (optional)
- **Metadata**: Title, description, author, publish date, thumbnails
- **Fallback**: oEmbed API when YouTube API unavailable

#### TikTok (`tiktok.com`)
- **Features**: Video previews, author information
- **API Integration**: oEmbed API
- **Metadata**: Title, author, video thumbnails
- **Limitations**: Limited metrics due to API restrictions

### 📱 Social Media Platforms

#### Twitter/X (`twitter.com`, `x.com`)
- **Enhanced Features**: Tweet metrics, thread detection
- **API Integration**: Twitter API v2 (optional)
- **Metadata**: Tweet content, author, engagement metrics
- **Fallback**: oEmbed API for basic tweet information

#### Instagram (`instagram.com`)
- **Features**: Post previews, author information
- **API Integration**: Instagram oEmbed API
- **Metadata**: Post title, author, basic information
- **Limitations**: Limited due to Instagram's API restrictions

#### Facebook (`facebook.com`)
- **Features**: Open Graph metadata extraction
- **API Integration**: Open Graph parsing only
- **Metadata**: Post title, description, images
- **Limitations**: Strict API restrictions, relies on Open Graph

#### LinkedIn (`linkedin.com`)
- **Features**: Professional content previews
- **API Integration**: Open Graph parsing
- **Metadata**: Article/post title, author, description
- **Focus**: Professional content optimization

### 🗨️ Discussion Platforms

#### Reddit (`reddit.com`)
- **Enhanced Features**: Post scores, comment counts, subreddit info
- **API Integration**: Reddit JSON API (no auth required)
- **Metadata**: Post title, author, subreddit, engagement metrics
- **Real-time**: Live score and comment updates

#### Telegram (`t.me`, `telegram.me`)
- **Enhanced Features**: Detailed entity type detection, enhanced metadata extraction
- **API Integration**: Advanced URL pattern analysis, Open Graph parsing
- **Metadata**: Channel/group names, entity types, message IDs, bot parameters
- **Supported Formats**:
  - Channel links: `t.me/channelname`
  - Group links: `t.me/groupname`
  - Message links: `t.me/channel/123`
  - Private channel messages: `t.me/c/1234567890/123`
  - Bot links: `t.me/botname`
  - Bot start links: `t.me/bot?start=param`
  - Thread messages: `t.me/channel/123/456`
- **Entity Detection**: Automatic detection of bots, channels, groups, messages
- **Fallback System**: Graceful degradation for invalid or inaccessible links

## Configuration

### API Keys Setup

Add your API keys in the Settings panel under "API Configuration":

```javascript
// YouTube Data API v3
youtubeApiKey: 'YOUR_YOUTUBE_API_KEY'

// Twitter API v2 Bearer Token
twitterBearerToken: 'YOUR_TWITTER_BEARER_TOKEN'

// Microlink API Key (for higher rate limits)
microlinkApiKey: 'YOUR_MICROLINK_API_KEY'
```

### Platform Settings

Enable/disable specific platforms in Settings > Preview Settings:

- ✅ YouTube - Enhanced video metadata
- ✅ Twitter/X - Tweet previews and metrics
- ✅ Instagram - Post previews
- ✅ TikTok - Video previews
- ✅ Reddit - Post and engagement data
- ✅ LinkedIn - Professional content
- ✅ Facebook - Basic Open Graph data
- ✅ Telegram - Channel and message links

### Preview Quality Settings

Choose preview quality level:
- **Low (Fast)**: Basic metadata only
- **Medium**: Standard metadata with some metrics
- **High (Detailed)**: Full metadata with all available metrics

### Cache Configuration

Configure cache behavior:
- **Duration**: 1 hour to 1 week
- **Refresh**: Smart refresh for dynamic content
- **Size**: Maximum cached previews

## Technical Implementation

### Architecture

```
PreviewService
├── PlatformHandlers
│   ├── YouTube Handler
│   ├── Twitter Handler
│   ├── Instagram Handler
│   ├── TikTok Handler
│   ├── Reddit Handler
│   ├── LinkedIn Handler
│   ├── Facebook Handler
│   └── Telegram Handler
├── Rate Limiting
├── Cache Management
└── Fallback System
```

### Rate Limiting

Each platform has specific rate limits:

- **Microlink**: 100 requests/day (free tier)
- **YouTube**: 10,000 requests/day
- **Twitter**: 300 requests/15 minutes
- **Reddit**: 60 requests/minute

### Fallback Mechanisms

1. **Primary**: Platform-specific API
2. **Secondary**: oEmbed API (if available)
3. **Tertiary**: Open Graph parsing
4. **Final**: Basic URL metadata

### Error Handling

- Graceful degradation on API failures
- Automatic retry with exponential backoff
- Comprehensive error logging
- User-friendly error messages

## Usage Examples

### Basic Usage

```javascript
// Get preview for any URL
const preview = await previewService.getPreview(url);

// Platform-specific data will be included
if (preview.platform === 'youtube') {
    console.log(`Duration: ${preview.duration}s`);
    console.log(`Views: ${preview.viewCount}`);
}
```

### Testing and Development

Use the built-in testing utilities:

```javascript
// Test all platforms
await testPreviews();

// Test specific URL
await testUrl('https://youtube.com/watch?v=...');

// Test cache performance
await testCache();

// Generate comprehensive report
await generatePreviewReport();
```

## Performance Optimization

### Caching Strategy

- **Static Content**: Long cache duration (1 week)
- **Dynamic Content**: Short cache duration (1 hour)
- **Social Media**: Medium cache duration (6 hours)

### Batch Processing

- Concurrent request limiting
- Request queuing for rate limit compliance
- Intelligent request prioritization

### Bandwidth Optimization

- Thumbnail size optimization
- Metadata field selection
- Compression for cached data

## Troubleshooting

### Common Issues

**Previews not loading:**
1. Check internet connection
2. Verify API keys in settings
3. Check browser console for errors
4. Test with preview tester utility

**Rate limit exceeded:**
1. Add API keys for higher limits
2. Reduce preview frequency
3. Clear cache to reset counters

**Platform-specific failures:**
1. Check platform API status
2. Verify URL format
3. Test with fallback methods

### Debug Tools

Use browser console commands:

```javascript
// Test all platforms
testPreviews()

// Test specific URL
testUrl('https://example.com')

// Check cache status
previewService.getCacheSize()

// Clear cache
previewService.clearCache()

// Generate debug report
generatePreviewReport()
```

## API Reference

### PreviewService Methods

```javascript
// Get preview with caching
getPreview(url, useCache = true)

// Clear all cached previews
clearCache()

// Get cache statistics
getCacheSize()

// Check rate limit status
checkRateLimit(service)
```

### Preview Data Structure

```javascript
{
    platform: 'youtube',           // Platform identifier
    title: 'Video Title',          // Content title
    description: 'Description',    // Content description
    image: 'thumbnail_url',        // Preview image
    author: 'Channel Name',        // Content author
    siteName: 'YouTube',           // Platform name
    url: 'original_url',           // Original URL
    favicon: 'favicon_url',        // Platform favicon
    
    // Platform-specific metadata
    duration: 180,                 // Video duration (seconds)
    viewCount: 1000000,           // View count
    likeCount: 50000,             // Like count
    publishDate: '2024-01-01',    // Publish date
    
    // Social metrics (when available)
    score: 1500,                  // Reddit score
    comments: 250,                // Comment count
    shares: 100                   // Share count
}
```

## Future Enhancements

### Planned Features

- **Real-time Updates**: Live metrics for dynamic content
- **Advanced Analytics**: Preview performance tracking
- **Custom Handlers**: User-defined platform handlers
- **Bulk Processing**: Batch preview generation
- **AI Enhancement**: AI-powered content analysis

### API Integrations

- **Spotify**: Music track previews
- **GitHub**: Repository information
- **Medium**: Article previews
- **Twitch**: Stream and clip previews

## Contributing

To add support for new platforms:

1. Create handler in `PlatformHandlers` class
2. Add platform detection logic
3. Implement metadata extraction
4. Add fallback mechanisms
5. Update configuration options
6. Add comprehensive tests

## License

This enhanced preview system is part of Link Manager Pro and follows the same MIT license terms.
