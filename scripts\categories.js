// Category Management System
class CategoryManager {
    constructor() {
        this.categories = [];
        this.activeCategory = 'all';
        this.loadCategories();
        this.setupEventListeners();
    }

    /**
     * Load categories from storage
     */
    loadCategories() {
        this.categories = storageManager.get(storageManager.keys.CATEGORIES, []);
        
        // Ensure default categories exist
        if (this.categories.length === 0) {
            this.categories = getConfig('CATEGORIES.DEFAULT_CATEGORIES', []);
            this.saveCategories();
        }
        
        this.renderCategories();
    }

    /**
     * Save categories to storage
     */
    saveCategories() {
        storageManager.set(storageManager.keys.CATEGORIES, this.categories);
        this.renderCategories();
    }

    /**
     * Get all categories
     */
    getCategories() {
        return this.categories;
    }

    /**
     * Get category by ID
     */
    getCategory(id) {
        return this.categories.find(cat => cat.id === id);
    }

    /**
     * Add new category
     */
    addCategory(categoryData) {
        const { name, icon = 'fas fa-folder', color = '#6366f1' } = categoryData;
        
        // Validate
        if (!name || name.trim().length === 0) {
            throw new Error('Category name is required');
        }

        if (this.categories.find(cat => cat.name.toLowerCase() === name.toLowerCase())) {
            throw new Error('Category with this name already exists');
        }

        if (this.categories.length >= getConfig('CATEGORIES.MAX_CATEGORIES', 20)) {
            throw new Error('Maximum number of categories reached');
        }

        const category = {
            id: this.generateCategoryId(name),
            name: name.trim(),
            icon,
            color,
            createdAt: new Date().toISOString(),
            linkCount: 0
        };

        this.categories.push(category);
        this.saveCategories();
        
        showSuccess(`Category "${name}" created successfully`);
        return category;
    }

    /**
     * Update category
     */
    updateCategory(id, updates) {
        const categoryIndex = this.categories.findIndex(cat => cat.id === id);
        
        if (categoryIndex === -1) {
            throw new Error('Category not found');
        }

        // Don't allow updating default categories
        if (id === 'all') {
            throw new Error('Cannot modify the "All Links" category');
        }

        // Validate name uniqueness if name is being updated
        if (updates.name) {
            const existingCategory = this.categories.find(cat => 
                cat.id !== id && cat.name.toLowerCase() === updates.name.toLowerCase()
            );
            if (existingCategory) {
                throw new Error('Category with this name already exists');
            }
        }

        this.categories[categoryIndex] = {
            ...this.categories[categoryIndex],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.saveCategories();
        showSuccess('Category updated successfully');
        
        return this.categories[categoryIndex];
    }

    /**
     * Delete category
     */
    deleteCategory(id) {
        if (id === 'all') {
            throw new Error('Cannot delete the "All Links" category');
        }

        const categoryIndex = this.categories.findIndex(cat => cat.id === id);
        
        if (categoryIndex === -1) {
            throw new Error('Category not found');
        }

        const category = this.categories[categoryIndex];
        
        // Check if category has links
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categoryLinks = links.filter(link => link.category === id);
        
        if (categoryLinks.length > 0) {
            // Move links to uncategorized or ask user
            modalManager.confirm(
                `Category "${category.name}" contains ${categoryLinks.length} link(s). What would you like to do?`,
                () => {
                    // Move links to uncategorized
                    const updatedLinks = links.map(link => 
                        link.category === id ? { ...link, category: '' } : link
                    );
                    storageManager.set(storageManager.keys.LINKS, updatedLinks);
                    
                    // Delete category
                    this.categories.splice(categoryIndex, 1);
                    this.saveCategories();
                    
                    // Switch to all links if this was active category
                    if (this.activeCategory === id) {
                        this.setActiveCategory('all');
                    }
                    
                    showSuccess(`Category "${category.name}" deleted and links moved to uncategorized`);
                },
                null,
                {
                    title: 'Delete Category',
                    confirmText: 'Move Links & Delete',
                    cancelText: 'Cancel'
                }
            );
            return;
        }

        // Delete category
        this.categories.splice(categoryIndex, 1);
        this.saveCategories();
        
        // Switch to all links if this was active category
        if (this.activeCategory === id) {
            this.setActiveCategory('all');
        }
        
        showSuccess(`Category "${category.name}" deleted successfully`);
    }

    /**
     * Set active category
     */
    setActiveCategory(categoryId) {
        this.activeCategory = categoryId;
        this.renderCategories();
        
        // Dispatch event for other components
        window.dispatchEvent(new CustomEvent('category-changed', {
            detail: { categoryId, category: this.getCategory(categoryId) }
        }));
    }

    /**
     * Get active category
     */
    getActiveCategory() {
        return this.activeCategory;
    }

    /**
     * Update link counts for all categories
     */
    updateLinkCounts() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        
        // Count links per category
        const counts = {};
        links.forEach(link => {
            const category = link.category || 'uncategorized';
            counts[category] = (counts[category] || 0) + 1;
        });

        // Update category objects
        this.categories.forEach(category => {
            if (category.id === 'all') {
                category.linkCount = links.length;
            } else {
                category.linkCount = counts[category.id] || 0;
            }
        });

        this.renderCategories();
    }

    /**
     * Render categories in sidebar
     */
    renderCategories() {
        const container = document.getElementById('categoryList');
        if (!container) return;

        container.innerHTML = '';

        this.categories.forEach(category => {
            const categoryElement = this.createCategoryElement(category);
            container.appendChild(categoryElement);
        });
    }

    /**
     * Create category element
     */
    createCategoryElement(category) {
        const element = document.createElement('div');
        element.className = `category-item ${this.activeCategory === category.id ? 'active' : ''}`;
        element.setAttribute('data-category-id', category.id);

        element.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 flex-1 min-w-0">
                    <div class="w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0" 
                         style="background: ${category.color}20; border: 1px solid ${category.color}40;">
                        <i class="${category.icon} text-sm" style="color: ${category.color};"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-white font-medium truncate">${this.escapeHtml(category.name)}</p>
                        <p class="text-gray-400 text-xs">${category.linkCount || 0} links</p>
                    </div>
                </div>
                ${category.id !== 'all' ? `
                    <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button class="edit-category-btn p-1 rounded hover:bg-white/10 transition-colors" 
                                data-category-id="${category.id}">
                            <i class="fas fa-edit text-xs text-gray-400 hover:text-white"></i>
                        </button>
                        <button class="delete-category-btn p-1 rounded hover:bg-white/10 transition-colors" 
                                data-category-id="${category.id}">
                            <i class="fas fa-trash text-xs text-gray-400 hover:text-red-400"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
        `;

        // Add hover effect class
        element.classList.add('group');

        // Add click handler
        element.addEventListener('click', (e) => {
            if (!e.target.closest('button')) {
                this.setActiveCategory(category.id);
            }
        });

        // Add edit button handler
        const editBtn = element.querySelector('.edit-category-btn');
        if (editBtn) {
            editBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showEditCategoryModal(category);
            });
        }

        // Add delete button handler
        const deleteBtn = element.querySelector('.delete-category-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteCategory(category.id);
            });
        }

        return element;
    }

    /**
     * Show add category modal
     */
    showAddCategoryModal() {
        const content = `
            <form id="categoryForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category Name *</label>
                    <input type="text" id="categoryName" required
                           class="form-input w-full px-4 py-3 rounded-xl"
                           placeholder="Enter category name"
                           maxlength="50">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Icon</label>
                    <div class="grid grid-cols-6 gap-2" id="iconGrid">
                        ${this.getIconOptions().map(icon => `
                            <button type="button" class="icon-option p-3 rounded-lg border border-white/20 hover:border-purple-500 transition-colors"
                                    data-icon="${icon}">
                                <i class="${icon} text-white"></i>
                            </button>
                        `).join('')}
                    </div>
                    <input type="hidden" id="categoryIcon" value="fas fa-folder">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Color</label>
                    <div class="grid grid-cols-8 gap-2" id="colorGrid">
                        ${this.getColorOptions().map(color => `
                            <button type="button" class="color-option w-8 h-8 rounded-lg border-2 border-white/20 hover:border-white transition-colors"
                                    style="background: ${color};" data-color="${color}"></button>
                        `).join('')}
                    </div>
                    <input type="hidden" id="categoryColor" value="#6366f1">
                </div>
            </form>
        `;

        const modal = modalManager.show({
            title: 'Add New Category',
            content,
            size: 'md',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' },
                { text: 'Add Category', className: 'glass-button-primary text-white', action: 'save' }
            ]
        });

        this.setupCategoryFormHandlers(modal);
    }

    /**
     * Show edit category modal
     */
    showEditCategoryModal(category) {
        const content = `
            <form id="categoryForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category Name *</label>
                    <input type="text" id="categoryName" required
                           class="form-input w-full px-4 py-3 rounded-xl"
                           placeholder="Enter category name"
                           value="${this.escapeHtml(category.name)}"
                           maxlength="50">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Icon</label>
                    <div class="grid grid-cols-6 gap-2" id="iconGrid">
                        ${this.getIconOptions().map(icon => `
                            <button type="button" class="icon-option p-3 rounded-lg border border-white/20 hover:border-purple-500 transition-colors ${icon === category.icon ? 'border-purple-500 bg-purple-500/20' : ''}"
                                    data-icon="${icon}">
                                <i class="${icon} text-white"></i>
                            </button>
                        `).join('')}
                    </div>
                    <input type="hidden" id="categoryIcon" value="${category.icon}">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Color</label>
                    <div class="grid grid-cols-8 gap-2" id="colorGrid">
                        ${this.getColorOptions().map(color => `
                            <button type="button" class="color-option w-8 h-8 rounded-lg border-2 transition-colors ${color === category.color ? 'border-white' : 'border-white/20 hover:border-white'}"
                                    style="background: ${color};" data-color="${color}"></button>
                        `).join('')}
                    </div>
                    <input type="hidden" id="categoryColor" value="${category.color}">
                </div>
                
                <input type="hidden" id="categoryId" value="${category.id}">
            </form>
        `;

        const modal = modalManager.show({
            title: 'Edit Category',
            content,
            size: 'md',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' },
                { text: 'Update Category', className: 'glass-button-primary text-white', action: 'save' }
            ]
        });

        this.setupCategoryFormHandlers(modal, true);
    }

    /**
     * Setup category form handlers
     */
    setupCategoryFormHandlers(modal, isEdit = false) {
        // Icon selection
        modal.querySelectorAll('.icon-option').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.querySelectorAll('.icon-option').forEach(b => {
                    b.classList.remove('border-purple-500', 'bg-purple-500/20');
                });
                btn.classList.add('border-purple-500', 'bg-purple-500/20');
                modal.querySelector('#categoryIcon').value = btn.dataset.icon;
            });
        });

        // Color selection
        modal.querySelectorAll('.color-option').forEach(btn => {
            btn.addEventListener('click', () => {
                modal.querySelectorAll('.color-option').forEach(b => {
                    b.classList.remove('border-white');
                    b.classList.add('border-white/20');
                });
                btn.classList.remove('border-white/20');
                btn.classList.add('border-white');
                modal.querySelector('#categoryColor').value = btn.dataset.color;
            });
        });

        // Form submission
        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'save') {
                const name = modal.querySelector('#categoryName').value.trim();
                const icon = modal.querySelector('#categoryIcon').value;
                const color = modal.querySelector('#categoryColor').value;

                try {
                    if (isEdit) {
                        const id = modal.querySelector('#categoryId').value;
                        this.updateCategory(id, { name, icon, color });
                    } else {
                        this.addCategory({ name, icon, color });
                    }
                    modalManager.close();
                } catch (error) {
                    showError(error.message);
                }
            } else if (e.detail.action === 'cancel') {
                modalManager.close();
            }
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add category button
        document.getElementById('addCategoryBtn')?.addEventListener('click', () => {
            this.showAddCategoryModal();
        });

        // Listen for storage changes
        window.addEventListener('storage-change', (e) => {
            if (e.detail.key === storageManager.keys.LINKS) {
                this.updateLinkCounts();
            }
        });
    }

    /**
     * Utility functions
     */
    
    generateCategoryId(name) {
        return name.toLowerCase()
                  .replace(/[^a-z0-9]/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-|-$/g, '') + '-' + Date.now().toString(36);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getIconOptions() {
        return [
            'fas fa-folder', 'fas fa-briefcase', 'fas fa-user', 'fas fa-graduation-cap',
            'fas fa-play', 'fas fa-tools', 'fas fa-star', 'fas fa-heart',
            'fas fa-code', 'fas fa-book', 'fas fa-music', 'fas fa-camera',
            'fas fa-shopping-cart', 'fas fa-car', 'fas fa-home', 'fas fa-globe',
            'fas fa-gamepad', 'fas fa-utensils', 'fas fa-plane', 'fas fa-dumbbell',
            'fas fa-palette', 'fas fa-microscope', 'fas fa-rocket', 'fas fa-leaf'
        ];
    }

    getColorOptions() {
        return [
            '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
            '#ec4899', '#f43f5e', '#ef4444', '#f97316',
            '#f59e0b', '#eab308', '#84cc16', '#22c55e',
            '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
            '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7'
        ];
    }
}

// Create global instance
const categoryManager = new CategoryManager();
