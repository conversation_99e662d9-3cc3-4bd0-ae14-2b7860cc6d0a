// Export/Import System
class ExportManager {
    constructor() {
        this.supportedFormats = getConfig('EXPORT.FORMATS', ['json', 'csv', 'markdown', 'html']);
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Export button
        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.showExportModal();
        });

        // Custom events
        window.addEventListener('show-export-modal', () => {
            this.showExportModal();
        });

        window.addEventListener('show-import-modal', () => {
            this.showImportModal();
        });
    }

    /**
     * Show export modal
     */
    showExportModal() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categories = categoryManager.getCategories();
        
        const content = `
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Export Options</h3>
                    <div class="grid grid-cols-2 gap-4">
                        ${this.supportedFormats.map(format => `
                            <label class="export-format-option glass-card p-4 rounded-xl cursor-pointer hover:bg-white/10 transition-colors">
                                <input type="radio" name="exportFormat" value="${format}" class="sr-only">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                                        <i class="${this.getFormatIcon(format)} text-white"></i>
                                    </div>
                                    <div>
                                        <p class="text-white font-medium">${format.toUpperCase()}</p>
                                        <p class="text-gray-400 text-sm">${this.getFormatDescription(format)}</p>
                                    </div>
                                </div>
                                <div class="format-check hidden mt-2">
                                    <i class="fas fa-check text-green-400"></i>
                                </div>
                            </label>
                        `).join('')}
                    </div>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-white mb-3">What to Export</h4>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="exportLinks" checked class="rounded">
                            <span class="text-white">Links (${links.length} items)</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="exportCategories" checked class="rounded">
                            <span class="text-white">Categories (${categories.length} items)</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="exportSettings" class="rounded">
                            <span class="text-white">Settings & Preferences</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-white mb-3">Filter Options</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-2">Category Filter</label>
                            <select id="categoryFilter" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="">All Categories</option>
                                ${categories.filter(cat => cat.id !== 'all').map(cat => `
                                    <option value="${cat.id}">${cat.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" id="favoritesOnly" class="rounded">
                                <span class="text-white">Favorites Only</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const modal = modalManager.show({
            title: 'Export Data',
            content,
            size: 'lg',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' },
                { text: 'Export', className: 'glass-button-primary text-white', action: 'export', icon: 'fas fa-download' }
            ]
        });

        this.setupExportModalHandlers(modal);
    }

    /**
     * Setup export modal handlers
     */
    setupExportModalHandlers(modal) {
        // Format selection
        const formatOptions = modal.querySelectorAll('.export-format-option');
        formatOptions.forEach(option => {
            option.addEventListener('click', () => {
                formatOptions.forEach(opt => {
                    opt.classList.remove('border-purple-500', 'bg-purple-500/20');
                    opt.querySelector('.format-check').classList.add('hidden');
                });
                option.classList.add('border-purple-500', 'bg-purple-500/20');
                option.querySelector('.format-check').classList.remove('hidden');
                option.querySelector('input').checked = true;
            });
        });

        // Set default format
        formatOptions[0]?.click();

        // Handle export action
        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'export') {
                this.handleExport(modal);
            } else if (e.detail.action === 'cancel') {
                modalManager.close();
            }
        });
    }

    /**
     * Handle export process
     */
    async handleExport(modal) {
        const format = modal.querySelector('input[name="exportFormat"]:checked')?.value;
        const includeLinks = modal.querySelector('#exportLinks').checked;
        const includeCategories = modal.querySelector('#exportCategories').checked;
        const includeSettings = modal.querySelector('#exportSettings').checked;
        const categoryFilter = modal.querySelector('#categoryFilter').value;
        const favoritesOnly = modal.querySelector('#favoritesOnly').checked;

        if (!format) {
            showError('Please select an export format');
            return;
        }

        try {
            const loadingToast = showLoading('Preparing export...');
            
            const exportData = await this.prepareExportData({
                includeLinks,
                includeCategories,
                includeSettings,
                categoryFilter,
                favoritesOnly
            });

            const result = await this.exportToFormat(exportData, format);
            
            hideToast(loadingToast);
            modalManager.close();
            
            showSuccess(`Data exported successfully as ${format.toUpperCase()}`);
            
        } catch (error) {
            console.error('Export failed:', error);
            showError('Export failed: ' + error.message);
        }
    }

    /**
     * Prepare export data based on options
     */
    async prepareExportData(options) {
        const data = {};

        if (options.includeLinks) {
            let links = storageManager.get(storageManager.keys.LINKS, []);
            
            // Apply filters
            if (options.categoryFilter) {
                links = links.filter(link => link.category === options.categoryFilter);
            }
            
            if (options.favoritesOnly) {
                links = links.filter(link => link.favorite);
            }
            
            data.links = links;
        }

        if (options.includeCategories) {
            data.categories = storageManager.get(storageManager.keys.CATEGORIES, []);
        }

        if (options.includeSettings) {
            data.settings = storageManager.get(storageManager.keys.SETTINGS, {});
        }

        return {
            exportDate: new Date().toISOString(),
            version: '1.0.0',
            format: 'Link Manager Pro Export',
            data
        };
    }

    /**
     * Export data to specific format
     */
    async exportToFormat(exportData, format) {
        switch (format) {
            case 'json':
                return this.exportToJSON(exportData);
            case 'csv':
                return this.exportToCSV(exportData);
            case 'markdown':
                return this.exportToMarkdown(exportData);
            case 'html':
                return this.exportToHTML(exportData);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    /**
     * Export to JSON
     */
    exportToJSON(data) {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        this.downloadFile(blob, `links-export-${this.getDateString()}.json`);
    }

    /**
     * Export to CSV
     */
    exportToCSV(data) {
        if (!data.data.links || data.data.links.length === 0) {
            throw new Error('No links to export');
        }

        const headers = ['Title', 'URL', 'Description', 'Category', 'Tags', 'Favorite', 'Created Date'];
        const rows = [headers];

        data.data.links.forEach(link => {
            const category = categoryManager.getCategory(link.category);
            rows.push([
                link.title || '',
                link.url || '',
                link.description || '',
                category?.name || '',
                (link.tags || []).join('; '),
                link.favorite ? 'Yes' : 'No',
                link.createdAt ? new Date(link.createdAt).toLocaleDateString() : ''
            ]);
        });

        const csvContent = rows.map(row => 
            row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
        ).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        this.downloadFile(blob, `links-export-${this.getDateString()}.csv`);
    }

    /**
     * Export to Markdown
     */
    exportToMarkdown(data) {
        let markdown = `# Link Manager Pro Export\n\n`;
        markdown += `Export Date: ${new Date(data.exportDate).toLocaleDateString()}\n\n`;

        if (data.data.links && data.data.links.length > 0) {
            markdown += `## Links (${data.data.links.length})\n\n`;
            
            // Group by category
            const linksByCategory = {};
            data.data.links.forEach(link => {
                const categoryId = link.category || 'uncategorized';
                if (!linksByCategory[categoryId]) {
                    linksByCategory[categoryId] = [];
                }
                linksByCategory[categoryId].push(link);
            });

            Object.entries(linksByCategory).forEach(([categoryId, links]) => {
                const category = categoryManager.getCategory(categoryId);
                const categoryName = category?.name || 'Uncategorized';
                
                markdown += `### ${categoryName}\n\n`;
                
                links.forEach(link => {
                    markdown += `- [${link.title || link.url}](${link.url})`;
                    if (link.description) {
                        markdown += ` - ${link.description}`;
                    }
                    if (link.favorite) {
                        markdown += ` ⭐`;
                    }
                    if (link.tags && link.tags.length > 0) {
                        markdown += ` (Tags: ${link.tags.join(', ')})`;
                    }
                    markdown += '\n';
                });
                
                markdown += '\n';
            });
        }

        if (data.data.categories && data.data.categories.length > 0) {
            markdown += `## Categories (${data.data.categories.length})\n\n`;
            data.data.categories.forEach(category => {
                if (category.id !== 'all') {
                    markdown += `- ${category.name}\n`;
                }
            });
        }

        const blob = new Blob([markdown], { type: 'text/markdown' });
        this.downloadFile(blob, `links-export-${this.getDateString()}.md`);
    }

    /**
     * Export to HTML
     */
    exportToHTML(data) {
        let html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Manager Pro Export</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #6366f1; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        h3 { color: #666; margin-top: 20px; }
        .link { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .link a { color: #6366f1; text-decoration: none; font-weight: 500; }
        .link a:hover { text-decoration: underline; }
        .description { color: #666; margin-top: 5px; font-size: 14px; }
        .tags { margin-top: 5px; }
        .tag { background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-right: 5px; }
        .favorite { color: #ffc107; }
        .meta { color: #999; font-size: 12px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Link Manager Pro Export</h1>
        <p class="meta">Export Date: ${new Date(data.exportDate).toLocaleDateString()}</p>
`;

        if (data.data.links && data.data.links.length > 0) {
            html += `<h2>Links (${data.data.links.length})</h2>`;
            
            // Group by category
            const linksByCategory = {};
            data.data.links.forEach(link => {
                const categoryId = link.category || 'uncategorized';
                if (!linksByCategory[categoryId]) {
                    linksByCategory[categoryId] = [];
                }
                linksByCategory[categoryId].push(link);
            });

            Object.entries(linksByCategory).forEach(([categoryId, links]) => {
                const category = categoryManager.getCategory(categoryId);
                const categoryName = category?.name || 'Uncategorized';
                
                html += `<h3>${categoryName}</h3>`;
                
                links.forEach(link => {
                    html += `<div class="link">`;
                    html += `<a href="${link.url}" target="_blank">${link.title || link.url}</a>`;
                    if (link.favorite) {
                        html += ` <span class="favorite">⭐</span>`;
                    }
                    if (link.description) {
                        html += `<div class="description">${link.description}</div>`;
                    }
                    if (link.tags && link.tags.length > 0) {
                        html += `<div class="tags">`;
                        link.tags.forEach(tag => {
                            html += `<span class="tag">${tag}</span>`;
                        });
                        html += `</div>`;
                    }
                    html += `</div>`;
                });
            });
        }

        html += `
    </div>
</body>
</html>`;

        const blob = new Blob([html], { type: 'text/html' });
        this.downloadFile(blob, `links-export-${this.getDateString()}.html`);
    }

    /**
     * Show import modal
     */
    showImportModal() {
        const content = `
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Import Data</h3>
                    <div class="border-2 border-dashed border-white/30 rounded-xl p-8 text-center">
                        <input type="file" id="importFile" accept=".json,.csv" class="hidden">
                        <div class="space-y-4">
                            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                                <i class="fas fa-upload text-white text-2xl"></i>
                            </div>
                            <div>
                                <p class="text-white font-medium mb-2">Choose a file to import</p>
                                <p class="text-gray-400 text-sm">Supports JSON and CSV formats</p>
                            </div>
                            <button type="button" id="selectFileBtn" class="glass-button-primary px-6 py-3 rounded-xl text-white font-medium">
                                Select File
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="importOptions" class="hidden">
                    <h4 class="text-md font-semibold text-white mb-3">Import Options</h4>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3">
                            <input type="radio" name="importMode" value="merge" checked class="rounded">
                            <span class="text-white">Merge with existing data</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="radio" name="importMode" value="replace" class="rounded">
                            <span class="text-white">Replace existing data</span>
                        </label>
                    </div>
                </div>
                
                <div id="fileInfo" class="hidden glass-card p-4 rounded-xl">
                    <h4 class="text-md font-semibold text-white mb-2">File Information</h4>
                    <div id="fileDetails" class="text-sm text-gray-300"></div>
                </div>
            </div>
        `;

        const modal = modalManager.show({
            title: 'Import Data',
            content,
            size: 'md',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' },
                { text: 'Import', className: 'glass-button-primary text-white', action: 'import', icon: 'fas fa-upload' }
            ]
        });

        this.setupImportModalHandlers(modal);
    }

    /**
     * Setup import modal handlers
     */
    setupImportModalHandlers(modal) {
        const fileInput = modal.querySelector('#importFile');
        const selectBtn = modal.querySelector('#selectFileBtn');
        const importOptions = modal.querySelector('#importOptions');
        const fileInfo = modal.querySelector('#fileInfo');

        selectBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleFileSelection(file, importOptions, fileInfo);
            }
        });

        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'import') {
                this.handleImport(modal);
            } else if (e.detail.action === 'cancel') {
                modalManager.close();
            }
        });
    }

    /**
     * Handle file selection
     */
    async handleFileSelection(file, importOptions, fileInfo) {
        try {
            const content = await this.readFile(file);
            let data;

            if (file.name.endsWith('.json')) {
                data = JSON.parse(content);
            } else if (file.name.endsWith('.csv')) {
                data = this.parseCSV(content);
            } else {
                throw new Error('Unsupported file format');
            }

            // Show file info
            const details = this.analyzeImportData(data);
            fileInfo.querySelector('#fileDetails').innerHTML = details;
            fileInfo.classList.remove('hidden');
            importOptions.classList.remove('hidden');

        } catch (error) {
            showError('Failed to read file: ' + error.message);
        }
    }

    /**
     * Handle import process
     */
    async handleImport(modal) {
        const fileInput = modal.querySelector('#importFile');
        const importMode = modal.querySelector('input[name="importMode"]:checked')?.value;

        if (!fileInput.files[0]) {
            showError('Please select a file to import');
            return;
        }

        try {
            const loadingToast = showLoading('Importing data...');
            
            const file = fileInput.files[0];
            const content = await this.readFile(file);
            let data;

            if (file.name.endsWith('.json')) {
                data = JSON.parse(content);
            } else if (file.name.endsWith('.csv')) {
                data = this.parseCSV(content);
            }

            const success = storageManager.importData(data, importMode === 'merge');
            
            hideToast(loadingToast);
            modalManager.close();
            
            if (success) {
                showSuccess('Data imported successfully');
                // Refresh the app
                window.location.reload();
            }
            
        } catch (error) {
            console.error('Import failed:', error);
            showError('Import failed: ' + error.message);
        }
    }

    /**
     * Utility methods
     */
    
    getFormatIcon(format) {
        const icons = {
            json: 'fas fa-code',
            csv: 'fas fa-table',
            markdown: 'fab fa-markdown',
            html: 'fab fa-html5'
        };
        return icons[format] || 'fas fa-file';
    }

    getFormatDescription(format) {
        const descriptions = {
            json: 'Complete data with all metadata',
            csv: 'Spreadsheet format for links only',
            markdown: 'Human-readable format',
            html: 'Web page format'
        };
        return descriptions[format] || 'Export format';
    }

    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    getDateString() {
        return new Date().toISOString().split('T')[0];
    }

    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    parseCSV(content) {
        // Simple CSV parser - in production, consider using a library like Papa Parse
        const lines = content.split('\n');
        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
        const links = [];

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
                const link = {
                    title: values[0] || '',
                    url: values[1] || '',
                    description: values[2] || '',
                    category: values[3] || '',
                    tags: values[4] ? values[4].split(';').map(t => t.trim()) : [],
                    favorite: values[5] === 'Yes',
                    createdAt: values[6] ? new Date(values[6]).toISOString() : new Date().toISOString()
                };
                if (link.url) {
                    links.push(link);
                }
            }
        }

        return {
            data: { links },
            format: 'CSV Import'
        };
    }

    analyzeImportData(data) {
        const links = data.data?.links || [];
        const categories = data.data?.categories || [];
        
        return `
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span>Links:</span>
                    <span class="font-medium">${links.length}</span>
                </div>
                <div class="flex justify-between">
                    <span>Categories:</span>
                    <span class="font-medium">${categories.length}</span>
                </div>
                <div class="flex justify-between">
                    <span>Format:</span>
                    <span class="font-medium">${data.format || 'Unknown'}</span>
                </div>
            </div>
        `;
    }
}

// Create global instance
const exportManager = new ExportManager();
