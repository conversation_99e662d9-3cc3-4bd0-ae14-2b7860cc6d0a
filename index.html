<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Manager Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <!-- Background Pattern -->
    <div class="fixed inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
    
    <!-- Main Container -->
    <div class="relative z-10 min-h-screen">
        <!-- Header -->
        <header class="glass-card mx-4 mt-4 p-6 rounded-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-link text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-white">Link Manager Pro</h1>
                        <p class="text-gray-300 text-sm">Organize your digital world</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="searchBtn" class="glass-button p-3 rounded-xl">
                        <i class="fas fa-search text-white"></i>
                    </button>
                    <button id="settingsBtn" class="glass-button p-3 rounded-xl">
                        <i class="fas fa-cog text-white"></i>
                    </button>
                    <button id="exportBtn" class="glass-button p-3 rounded-xl">
                        <i class="fas fa-download text-white"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex flex-col lg:flex-row gap-6 p-4">
            <!-- Sidebar -->
            <aside id="sidebar" class="lg:w-80 glass-card rounded-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-white">Categories</h2>
                    <button id="addCategoryBtn" class="glass-button p-2 rounded-lg">
                        <i class="fas fa-plus text-white text-sm"></i>
                    </button>
                </div>
                <div id="categoryList" class="space-y-2">
                    <!-- Categories will be populated here -->
                </div>
                
                <div class="mt-8">
                    <button id="addLinkBtn" class="w-full glass-button-primary p-4 rounded-xl text-white font-semibold flex items-center justify-center space-x-2 hover:scale-105 transition-transform">
                        <i class="fas fa-plus"></i>
                        <span>Add New Link</span>
                    </button>
                </div>
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1">
                <!-- Search Bar -->
                <div id="searchContainer" class="glass-card rounded-2xl p-4 mb-6 hidden">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="Search links..." 
                               class="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pl-12 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-300"></i>
                    </div>
                </div>

                <!-- Links Grid -->
                <div id="linksContainer" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- Links will be populated here -->
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="glass-card rounded-2xl p-12 text-center">
                    <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-link text-white text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-white mb-4">No Links Yet</h3>
                    <p class="text-gray-300 mb-8">Start building your digital library by adding your first link</p>
                    <button id="addFirstLinkBtn" class="glass-button-primary px-8 py-3 rounded-xl text-white font-semibold hover:scale-105 transition-transform">
                        Add Your First Link
                    </button>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modalContainer"></div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="components/toast.js"></script>
    <script src="components/modal.js"></script>
    <script src="components/linkCard.js"></script>
    <script src="components/sidebar.js"></script>
    <script src="scripts/storage.js"></script>
    <script src="scripts/preview-service.js"></script>
    <script src="scripts/categories.js"></script>
    <script src="scripts/export.js"></script>
    <script src="scripts/settings.js"></script>
    <script src="scripts/app.js"></script>
</body>
</html>
