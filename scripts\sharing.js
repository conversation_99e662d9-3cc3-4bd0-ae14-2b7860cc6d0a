// Sharing Service for Link Manager Pro
class SharingService {
    constructor() {
        this.platforms = getConfig('SHARING.PLATFORMS', ['telegram', 'whatsapp', 'email', 'clipboard']);
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Share button
        document.getElementById('shareBtn')?.addEventListener('click', () => {
            this.showShareModal();
        });
    }

    /**
     * Show share modal
     */
    showShareModal() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categories = categoryManager.getCategories();
        
        const content = `
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Share Your Collection</h3>
                    <p class="text-gray-300 text-sm mb-4">Choose what to share and how to share it</p>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-white mb-3">What to Share</h4>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="shareLinks" checked class="rounded">
                            <span class="text-white">Links (${links.length} items)</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="shareCategories" checked class="rounded">
                            <span class="text-white">Categories (${categories.length} items)</span>
                        </label>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" id="shareFavoritesOnly" class="rounded">
                            <span class="text-white">Favorites Only</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-white mb-3">Share Format</h4>
                    <div class="grid grid-cols-3 gap-3">
                        <label class="share-format-option glass-card p-3 rounded-xl cursor-pointer hover:bg-white/10 transition-colors">
                            <input type="radio" name="shareFormat" value="html" checked class="sr-only">
                            <div class="text-center">
                                <i class="fab fa-html5 text-2xl text-orange-400 mb-2"></i>
                                <p class="text-white text-sm font-medium">HTML Page</p>
                                <p class="text-gray-400 text-xs">Interactive webpage</p>
                            </div>
                        </label>
                        <label class="share-format-option glass-card p-3 rounded-xl cursor-pointer hover:bg-white/10 transition-colors">
                            <input type="radio" name="shareFormat" value="markdown" class="sr-only">
                            <div class="text-center">
                                <i class="fab fa-markdown text-2xl text-blue-400 mb-2"></i>
                                <p class="text-white text-sm font-medium">Markdown</p>
                                <p class="text-gray-400 text-xs">Text format</p>
                            </div>
                        </label>
                        <label class="share-format-option glass-card p-3 rounded-xl cursor-pointer hover:bg-white/10 transition-colors">
                            <input type="radio" name="shareFormat" value="json" class="sr-only">
                            <div class="text-center">
                                <i class="fas fa-code text-2xl text-green-400 mb-2"></i>
                                <p class="text-white text-sm font-medium">JSON</p>
                                <p class="text-gray-400 text-xs">Data format</p>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-white mb-3">Share Via</h4>
                    <div class="grid grid-cols-2 gap-3">
                        ${this.platforms.map(platform => `
                            <button class="share-platform-btn glass-button p-4 rounded-xl text-white hover:scale-105 transition-transform"
                                    data-platform="${platform}">
                                <div class="flex items-center space-x-3">
                                    <i class="${this.getPlatformIcon(platform)} text-xl"></i>
                                    <span class="font-medium">${this.getPlatformName(platform)}</span>
                                </div>
                            </button>
                        `).join('')}
                    </div>
                </div>
                
                <div class="border-t border-white/20 pt-4">
                    <label class="flex items-center space-x-3">
                        <input type="checkbox" id="passwordProtect" class="rounded">
                        <span class="text-white">Password protect shared data</span>
                    </label>
                    <div id="passwordSection" class="mt-3 hidden">
                        <input type="password" id="sharePassword" placeholder="Enter password" 
                               class="form-input w-full px-4 py-2 rounded-xl">
                    </div>
                </div>
            </div>
        `;

        const modal = modalManager.show({
            title: 'Share Collection',
            content,
            size: 'lg',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' }
            ]
        });

        this.setupShareModalHandlers(modal);
    }

    /**
     * Setup share modal handlers
     */
    setupShareModalHandlers(modal) {
        // Format selection
        const formatOptions = modal.querySelectorAll('.share-format-option');
        formatOptions.forEach(option => {
            option.addEventListener('click', () => {
                formatOptions.forEach(opt => {
                    opt.classList.remove('border-purple-500', 'bg-purple-500/20');
                });
                option.classList.add('border-purple-500', 'bg-purple-500/20');
                option.querySelector('input').checked = true;
            });
        });

        // Password protection toggle
        const passwordCheckbox = modal.querySelector('#passwordProtect');
        const passwordSection = modal.querySelector('#passwordSection');
        
        passwordCheckbox.addEventListener('change', () => {
            if (passwordCheckbox.checked) {
                passwordSection.classList.remove('hidden');
            } else {
                passwordSection.classList.add('hidden');
            }
        });

        // Platform buttons
        const platformBtns = modal.querySelectorAll('.share-platform-btn');
        platformBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const platform = btn.getAttribute('data-platform');
                this.handleShare(modal, platform);
            });
        });

        // Modal actions
        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'cancel') {
                modalManager.close();
            }
        });
    }

    /**
     * Handle sharing to specific platform
     */
    async handleShare(modal, platform) {
        try {
            const shareData = await this.prepareShareData(modal);
            
            switch (platform) {
                case 'clipboard':
                    await this.shareToClipboard(shareData);
                    break;
                case 'email':
                    this.shareToEmail(shareData);
                    break;
                case 'telegram':
                    this.shareToTelegram(shareData);
                    break;
                case 'whatsapp':
                    this.shareToWhatsApp(shareData);
                    break;
                case 'twitter':
                    this.shareToTwitter(shareData);
                    break;
                default:
                    throw new Error(`Unsupported platform: ${platform}`);
            }
            
            modalManager.close();
            showSuccess(`Shared successfully via ${this.getPlatformName(platform)}`);
            
        } catch (error) {
            console.error('Share failed:', error);
            showError('Share failed: ' + error.message);
        }
    }

    /**
     * Prepare share data
     */
    async prepareShareData(modal) {
        const includeLinks = modal.querySelector('#shareLinks').checked;
        const includeCategories = modal.querySelector('#shareCategories').checked;
        const favoritesOnly = modal.querySelector('#shareFavoritesOnly').checked;
        const format = modal.querySelector('input[name="shareFormat"]:checked').value;
        const passwordProtect = modal.querySelector('#passwordProtect').checked;
        const password = modal.querySelector('#sharePassword')?.value;

        let links = storageManager.get(storageManager.keys.LINKS, []);
        
        if (favoritesOnly) {
            links = links.filter(link => link.favorite);
        }

        const data = {
            shareDate: new Date().toISOString(),
            format: 'Link Manager Pro Share',
            data: {}
        };

        if (includeLinks) {
            data.data.links = links;
        }

        if (includeCategories) {
            data.data.categories = storageManager.get(storageManager.keys.CATEGORIES, []);
        }

        // Apply password protection if enabled
        if (passwordProtect && password) {
            data.passwordProtected = true;
            data.data = await this.encryptData(data.data, password);
        }

        // Convert to requested format
        return this.convertToFormat(data, format);
    }

    /**
     * Convert data to specific format
     */
    convertToFormat(data, format) {
        switch (format) {
            case 'json':
                return {
                    content: JSON.stringify(data, null, 2),
                    mimeType: 'application/json',
                    filename: `links-share-${this.getDateString()}.json`
                };
            
            case 'markdown':
                return {
                    content: this.convertToMarkdown(data),
                    mimeType: 'text/markdown',
                    filename: `links-share-${this.getDateString()}.md`
                };
            
            case 'html':
                return {
                    content: this.convertToHTML(data),
                    mimeType: 'text/html',
                    filename: `links-share-${this.getDateString()}.html`
                };
            
            default:
                throw new Error(`Unsupported format: ${format}`);
        }
    }

    /**
     * Convert to Markdown format
     */
    convertToMarkdown(data) {
        let markdown = `# My Link Collection\n\n`;
        markdown += `Shared on: ${new Date(data.shareDate).toLocaleDateString()}\n\n`;

        if (data.data.links && data.data.links.length > 0) {
            markdown += `## Links (${data.data.links.length})\n\n`;
            
            data.data.links.forEach(link => {
                markdown += `### [${link.title || link.url}](${link.url})\n\n`;
                if (link.description) {
                    markdown += `${link.description}\n\n`;
                }
                if (link.tags && link.tags.length > 0) {
                    markdown += `**Tags:** ${link.tags.join(', ')}\n\n`;
                }
                markdown += `---\n\n`;
            });
        }

        return markdown;
    }

    /**
     * Convert to HTML format
     */
    convertToHTML(data) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Link Collection</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 40px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
        h1 { color: white; text-align: center; margin-bottom: 10px; }
        .subtitle { color: rgba(255,255,255,0.8); text-align: center; margin-bottom: 40px; }
        .link-card { background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.2); }
        .link-title { color: white; font-size: 18px; font-weight: 600; margin-bottom: 10px; }
        .link-url { color: #60a5fa; text-decoration: none; font-size: 14px; }
        .link-url:hover { text-decoration: underline; }
        .link-description { color: rgba(255,255,255,0.8); margin-top: 10px; line-height: 1.5; }
        .link-tags { margin-top: 10px; }
        .tag { background: rgba(255,255,255,0.2); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-right: 5px; }
        .stats { text-align: center; color: rgba(255,255,255,0.7); font-size: 14px; margin-top: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 My Link Collection</h1>
        <p class="subtitle">Shared on ${new Date(data.shareDate).toLocaleDateString()}</p>
        
        ${data.data.links ? data.data.links.map(link => `
            <div class="link-card">
                <div class="link-title">${link.title || 'Untitled'}</div>
                <a href="${link.url}" target="_blank" class="link-url">${link.url}</a>
                ${link.description ? `<div class="link-description">${link.description}</div>` : ''}
                ${link.tags && link.tags.length > 0 ? `
                    <div class="link-tags">
                        ${link.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        `).join('') : ''}
        
        <div class="stats">
            ${data.data.links ? data.data.links.length : 0} links shared • Generated by Link Manager Pro
        </div>
    </div>
</body>
</html>`;
    }

    /**
     * Share to clipboard
     */
    async shareToClipboard(shareData) {
        try {
            await navigator.clipboard.writeText(shareData.content);
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = shareData.content;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    /**
     * Share to email
     */
    shareToEmail(shareData) {
        const subject = encodeURIComponent('My Link Collection');
        const body = encodeURIComponent(shareData.content);
        window.open(`mailto:?subject=${subject}&body=${body}`);
    }

    /**
     * Share to Telegram
     */
    shareToTelegram(shareData) {
        const text = encodeURIComponent(`Check out my link collection!\n\n${shareData.content.substring(0, 500)}...`);
        window.open(`https://t.me/share/url?text=${text}`, '_blank');
    }

    /**
     * Share to WhatsApp
     */
    shareToWhatsApp(shareData) {
        const text = encodeURIComponent(`Check out my link collection!\n\n${shareData.content.substring(0, 500)}...`);
        window.open(`https://wa.me/?text=${text}`, '_blank');
    }

    /**
     * Share to Twitter
     */
    shareToTwitter(shareData) {
        const text = encodeURIComponent(`Check out my curated link collection! 🔗✨`);
        window.open(`https://twitter.com/intent/tweet?text=${text}`, '_blank');
    }

    /**
     * Encrypt data (simple implementation)
     */
    async encryptData(data, password) {
        // Simple base64 encoding with password (not secure, just for demo)
        const jsonString = JSON.stringify(data);
        const encoded = btoa(password + '::' + jsonString);
        return { encrypted: encoded };
    }

    /**
     * Utility functions
     */
    
    getPlatformIcon(platform) {
        const icons = {
            clipboard: 'fas fa-clipboard',
            email: 'fas fa-envelope',
            telegram: 'fab fa-telegram',
            whatsapp: 'fab fa-whatsapp',
            twitter: 'fab fa-twitter'
        };
        return icons[platform] || 'fas fa-share';
    }

    getPlatformName(platform) {
        const names = {
            clipboard: 'Clipboard',
            email: 'Email',
            telegram: 'Telegram',
            whatsapp: 'WhatsApp',
            twitter: 'Twitter'
        };
        return names[platform] || platform;
    }

    getDateString() {
        return new Date().toISOString().split('T')[0];
    }
}

// Create global instance
const sharingService = new SharingService();
