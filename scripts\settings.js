// Settings Management System
class SettingsManager {
    constructor() {
        this.settings = {};
        this.defaultSettings = {
            theme: 'dark',
            viewMode: 'grid',
            autoPreview: true,
            notifications: true,
            keyboardShortcuts: true,
            autoBackup: true,
            linksPerPage: 12,
            defaultCategory: '',
            language: 'en',
            dateFormat: 'relative',
            openLinksInNewTab: true,
            showLinkPreviews: true,
            compactMode: false,
            animationsEnabled: true,

            // Preview settings
            previewQuality: 'medium',
            cacheDuration: 24,

            // Platform settings
            enableYouTube: true,
            enableTwitter: true,
            enableInstagram: true,
            enableTikTok: true,
            enableReddit: true,
            enableLinkedIn: true,
            enableFacebook: true,
            enableTelegram: true,

            // API keys (empty by default)
            youtubeApiKey: '',
            twitterBearerToken: '',
            microlinkApiKey: ''
        };
        
        this.loadSettings();
        this.setupEventListeners();
    }

    /**
     * Load settings from storage
     */
    loadSettings() {
        const stored = storageManager.get(storageManager.keys.SETTINGS, {});
        this.settings = { ...this.defaultSettings, ...stored };
        this.applySettings();
    }

    /**
     * Save settings to storage
     */
    saveSettings() {
        storageManager.set(storageManager.keys.SETTINGS, this.settings);
        this.applySettings();
        
        // Dispatch settings change event
        window.dispatchEvent(new CustomEvent('settings-changed', {
            detail: { settings: this.settings }
        }));
    }

    /**
     * Get setting value
     */
    get(key, defaultValue = null) {
        return this.settings[key] ?? defaultValue;
    }

    /**
     * Set setting value
     */
    set(key, value) {
        this.settings[key] = value;
        this.saveSettings();
    }

    /**
     * Reset settings to defaults
     */
    reset() {
        this.settings = { ...this.defaultSettings };
        this.saveSettings();
        showSuccess('Settings reset to defaults');
    }

    /**
     * Apply settings to the application
     */
    applySettings() {
        // Apply theme
        this.applyTheme();
        
        // Apply view mode
        this.applyViewMode();
        
        // Apply other visual settings
        this.applyVisualSettings();
        
        // Apply keyboard shortcuts
        if (this.settings.keyboardShortcuts) {
            this.enableKeyboardShortcuts();
        } else {
            this.disableKeyboardShortcuts();
        }
    }

    /**
     * Apply theme settings
     */
    applyTheme() {
        const theme = this.settings.theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        // Update theme-specific styles if needed
        if (theme === 'light') {
            // Light theme adjustments
            document.body.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.1)');
        } else {
            // Dark theme (default)
            document.body.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.1)');
        }
    }

    /**
     * Apply view mode settings
     */
    applyViewMode() {
        const container = document.getElementById('linksContainer');
        if (!container) return;

        const viewMode = this.settings.viewMode;
        
        if (viewMode === 'list') {
            container.className = 'space-y-4';
        } else {
            container.className = 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6';
        }
    }

    /**
     * Apply visual settings
     */
    applyVisualSettings() {
        // Compact mode
        if (this.settings.compactMode) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }

        // Animations
        if (!this.settings.animationsEnabled) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Settings button
        document.getElementById('settingsBtn')?.addEventListener('click', () => {
            this.showSettingsModal();
        });

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.settings.keyboardShortcuts) {
                this.handleKeyboardShortcut(e);
            }
        });
    }

    /**
     * Show settings modal
     */
    showSettingsModal() {
        const content = `
            <div class="space-y-6 max-h-96 overflow-y-auto">
                <!-- Appearance Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Appearance</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Theme</label>
                            <select id="themeSelect" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="dark" ${this.settings.theme === 'dark' ? 'selected' : ''}>Dark</option>
                                <option value="light" ${this.settings.theme === 'light' ? 'selected' : ''}>Light</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">View Mode</label>
                            <select id="viewModeSelect" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="grid" ${this.settings.viewMode === 'grid' ? 'selected' : ''}>Grid</option>
                                <option value="list" ${this.settings.viewMode === 'list' ? 'selected' : ''}>List</option>
                            </select>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-white">Compact Mode</span>
                            <label class="switch">
                                <input type="checkbox" id="compactMode" ${this.settings.compactMode ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-white">Animations</span>
                            <label class="switch">
                                <input type="checkbox" id="animationsEnabled" ${this.settings.animationsEnabled ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Preview Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Preview Settings</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-white">Auto-fetch Link Previews</span>
                            <label class="switch">
                                <input type="checkbox" id="autoPreview" ${this.settings.autoPreview ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-white">Show Link Previews</span>
                            <label class="switch">
                                <input type="checkbox" id="showLinkPreviews" ${this.settings.showLinkPreviews ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Preview Quality</label>
                            <select id="previewQuality" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="low" ${this.settings.previewQuality === 'low' ? 'selected' : ''}>Low (Fast)</option>
                                <option value="medium" ${this.settings.previewQuality === 'medium' ? 'selected' : ''}>Medium</option>
                                <option value="high" ${this.settings.previewQuality === 'high' ? 'selected' : ''}>High (Detailed)</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Cache Duration (hours)</label>
                            <select id="cacheDuration" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="1" ${this.settings.cacheDuration === 1 ? 'selected' : ''}>1 hour</option>
                                <option value="6" ${this.settings.cacheDuration === 6 ? 'selected' : ''}>6 hours</option>
                                <option value="24" ${this.settings.cacheDuration === 24 ? 'selected' : ''}>24 hours</option>
                                <option value="168" ${this.settings.cacheDuration === 168 ? 'selected' : ''}>1 week</option>
                            </select>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-white mb-3">Platform Support</h4>
                            <div class="grid grid-cols-2 gap-3">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableYouTube" ${this.settings.enableYouTube !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">YouTube</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableTwitter" ${this.settings.enableTwitter !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">Twitter/X</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableInstagram" ${this.settings.enableInstagram !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">Instagram</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableTikTok" ${this.settings.enableTikTok !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">TikTok</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableReddit" ${this.settings.enableReddit !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">Reddit</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="enableLinkedIn" ${this.settings.enableLinkedIn !== false ? 'checked' : ''} class="rounded">
                                    <span class="text-sm text-white">LinkedIn</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Behavior Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Behavior</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-white">Open Links in New Tab</span>
                            <label class="switch">
                                <input type="checkbox" id="openLinksInNewTab" ${this.settings.openLinksInNewTab ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-white">Keyboard Shortcuts</span>
                            <label class="switch">
                                <input type="checkbox" id="keyboardShortcuts" ${this.settings.keyboardShortcuts ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Links per Page</label>
                            <select id="linksPerPage" class="form-input w-full px-4 py-2 rounded-xl">
                                <option value="6" ${this.settings.linksPerPage === 6 ? 'selected' : ''}>6</option>
                                <option value="12" ${this.settings.linksPerPage === 12 ? 'selected' : ''}>12</option>
                                <option value="24" ${this.settings.linksPerPage === 24 ? 'selected' : ''}>24</option>
                                <option value="48" ${this.settings.linksPerPage === 48 ? 'selected' : ''}>48</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- API Configuration -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">API Configuration</h3>
                    <p class="text-gray-300 text-sm mb-4">Add API keys for enhanced preview features (optional)</p>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">YouTube API Key</label>
                            <input type="password" id="youtubeApiKey"
                                   class="form-input w-full px-4 py-2 rounded-xl"
                                   placeholder="Enter YouTube Data API v3 key"
                                   value="${this.settings.youtubeApiKey || ''}">
                            <p class="text-xs text-gray-400 mt-1">For enhanced video metadata and statistics</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Twitter Bearer Token</label>
                            <input type="password" id="twitterBearerToken"
                                   class="form-input w-full px-4 py-2 rounded-xl"
                                   placeholder="Enter Twitter API v2 Bearer Token"
                                   value="${this.settings.twitterBearerToken || ''}">
                            <p class="text-xs text-gray-400 mt-1">For enhanced tweet metadata and metrics</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Microlink API Key</label>
                            <input type="password" id="microlinkApiKey"
                                   class="form-input w-full px-4 py-2 rounded-xl"
                                   placeholder="Enter Microlink API key"
                                   value="${this.settings.microlinkApiKey || ''}">
                            <p class="text-xs text-gray-400 mt-1">For higher rate limits and premium features</p>
                        </div>

                        <div class="bg-blue-500/20 border border-blue-500/30 rounded-xl p-3">
                            <div class="flex items-start space-x-2">
                                <i class="fas fa-info-circle text-blue-400 mt-0.5"></i>
                                <div class="text-sm text-blue-200">
                                    <p class="font-medium mb-1">API Keys are Optional</p>
                                    <p>The app works without API keys using free services. Adding keys enables enhanced features like detailed metrics and higher rate limits.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data & Privacy -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Data & Privacy</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-white">Auto Backup</span>
                            <label class="switch">
                                <input type="checkbox" id="autoBackup" ${this.settings.autoBackup ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-white">Notifications</span>
                            <label class="switch">
                                <input type="checkbox" id="notifications" ${this.settings.notifications ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="space-y-2">
                            <button id="clearCacheBtn" class="w-full glass-button p-3 rounded-xl text-white text-sm">
                                <i class="fas fa-trash mr-2"></i>
                                Clear Preview Cache
                            </button>
                            <button id="clearApiKeysBtn" class="w-full glass-button p-3 rounded-xl text-white text-sm">
                                <i class="fas fa-key mr-2"></i>
                                Clear API Keys
                            </button>
                            <button id="exportDataBtn" class="w-full glass-button p-3 rounded-xl text-white text-sm">
                                <i class="fas fa-download mr-2"></i>
                                Export All Data
                            </button>
                            <button id="resetSettingsBtn" class="w-full glass-button p-3 rounded-xl text-red-400 text-sm">
                                <i class="fas fa-undo mr-2"></i>
                                Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Storage Info -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Storage Information</h3>
                    <div id="storageInfo" class="glass-card p-4 rounded-xl">
                        ${this.getStorageInfoHTML()}
                    </div>
                </div>
            </div>
        `;

        const modal = modalManager.show({
            title: 'Settings',
            content,
            size: 'lg',
            actions: [
                { text: 'Cancel', className: 'glass-button text-white', action: 'cancel' },
                { text: 'Save Settings', className: 'glass-button-primary text-white', action: 'save' }
            ]
        });

        this.setupSettingsModalHandlers(modal);
    }

    /**
     * Setup settings modal handlers
     */
    setupSettingsModalHandlers(modal) {
        // Save settings
        modal.addEventListener('modal-action', (e) => {
            if (e.detail.action === 'save') {
                this.saveSettingsFromModal(modal);
                modalManager.close();
                showSuccess('Settings saved successfully');
            } else if (e.detail.action === 'cancel') {
                modalManager.close();
            }
        });

        // Clear cache button
        modal.querySelector('#clearCacheBtn')?.addEventListener('click', () => {
            previewService.clearCache();
            showSuccess('Preview cache cleared');
        });

        // Clear API keys button
        modal.querySelector('#clearApiKeysBtn')?.addEventListener('click', () => {
            modalManager.confirm(
                'Are you sure you want to clear all API keys?',
                () => {
                    modal.querySelector('#youtubeApiKey').value = '';
                    modal.querySelector('#twitterBearerToken').value = '';
                    modal.querySelector('#microlinkApiKey').value = '';
                    showSuccess('API keys cleared');
                },
                null,
                {
                    title: 'Clear API Keys',
                    confirmText: 'Clear',
                    cancelText: 'Cancel'
                }
            );
        });

        // Export data button
        modal.querySelector('#exportDataBtn')?.addEventListener('click', () => {
            modalManager.close();
            exportManager.showExportModal();
        });

        // Reset settings button
        modal.querySelector('#resetSettingsBtn')?.addEventListener('click', () => {
            modalManager.confirm(
                'Are you sure you want to reset all settings to defaults?',
                () => {
                    this.reset();
                    modalManager.close();
                },
                null,
                {
                    title: 'Reset Settings',
                    confirmText: 'Reset',
                    cancelText: 'Cancel'
                }
            );
        });
    }

    /**
     * Save settings from modal
     */
    saveSettingsFromModal(modal) {
        // Appearance settings
        this.settings.theme = modal.querySelector('#themeSelect').value;
        this.settings.viewMode = modal.querySelector('#viewModeSelect').value;
        this.settings.compactMode = modal.querySelector('#compactMode').checked;
        this.settings.animationsEnabled = modal.querySelector('#animationsEnabled').checked;

        // Preview settings
        this.settings.autoPreview = modal.querySelector('#autoPreview').checked;
        this.settings.showLinkPreviews = modal.querySelector('#showLinkPreviews').checked;
        this.settings.previewQuality = modal.querySelector('#previewQuality').value;
        this.settings.cacheDuration = parseInt(modal.querySelector('#cacheDuration').value);

        // Platform settings
        this.settings.enableYouTube = modal.querySelector('#enableYouTube').checked;
        this.settings.enableTwitter = modal.querySelector('#enableTwitter').checked;
        this.settings.enableInstagram = modal.querySelector('#enableInstagram').checked;
        this.settings.enableTikTok = modal.querySelector('#enableTikTok').checked;
        this.settings.enableReddit = modal.querySelector('#enableReddit').checked;
        this.settings.enableLinkedIn = modal.querySelector('#enableLinkedIn').checked;

        // API keys
        this.settings.youtubeApiKey = modal.querySelector('#youtubeApiKey').value;
        this.settings.twitterBearerToken = modal.querySelector('#twitterBearerToken').value;
        this.settings.microlinkApiKey = modal.querySelector('#microlinkApiKey').value;

        // Behavior settings
        this.settings.openLinksInNewTab = modal.querySelector('#openLinksInNewTab').checked;
        this.settings.keyboardShortcuts = modal.querySelector('#keyboardShortcuts').checked;
        this.settings.linksPerPage = parseInt(modal.querySelector('#linksPerPage').value);
        this.settings.autoBackup = modal.querySelector('#autoBackup').checked;
        this.settings.notifications = modal.querySelector('#notifications').checked;

        this.saveSettings();
    }

    /**
     * Get storage info HTML
     */
    getStorageInfoHTML() {
        const info = storageManager.getStorageInfo();
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const categories = storageManager.get(storageManager.keys.CATEGORIES, []);
        const backups = storageManager.getBackups();

        return `
            <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-300">Storage Used:</span>
                    <span class="text-white">${info.usedMB || 0} MB (${info.usagePercent || 0}%)</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-300">Links:</span>
                    <span class="text-white">${links.length}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-300">Categories:</span>
                    <span class="text-white">${categories.length}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-300">Backups:</span>
                    <span class="text-white">${backups.length}</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2 mt-2">
                    <div class="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full" 
                         style="width: ${Math.min(info.usagePercent || 0, 100)}%"></div>
                </div>
            </div>
        `;
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcut(e) {
        const shortcuts = getConfig('SHORTCUTS', {});
        
        // Check for modifier keys
        const isCtrl = e.ctrlKey || e.metaKey;
        const isShift = e.shiftKey;
        const isAlt = e.altKey;

        // Prevent shortcuts when typing in inputs
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        // Add new link (Ctrl+N)
        if (isCtrl && e.key === 'n') {
            e.preventDefault();
            document.getElementById('addLinkBtn')?.click();
        }

        // Search (Ctrl+F)
        if (isCtrl && e.key === 'f') {
            e.preventDefault();
            document.getElementById('searchBtn')?.click();
        }

        // Export (Ctrl+E)
        if (isCtrl && e.key === 'e') {
            e.preventDefault();
            document.getElementById('exportBtn')?.click();
        }

        // Settings (Ctrl+,)
        if (isCtrl && e.key === ',') {
            e.preventDefault();
            document.getElementById('settingsBtn')?.click();
        }

        // Escape key
        if (e.key === 'Escape') {
            // Close modals, search, etc.
            if (modalManager.activeModal) {
                modalManager.close();
            }
            
            const searchContainer = document.getElementById('searchContainer');
            if (searchContainer && !searchContainer.classList.contains('hidden')) {
                searchContainer.classList.add('hidden');
            }
        }
    }

    /**
     * Enable keyboard shortcuts
     */
    enableKeyboardShortcuts() {
        document.body.classList.remove('no-shortcuts');
    }

    /**
     * Disable keyboard shortcuts
     */
    disableKeyboardShortcuts() {
        document.body.classList.add('no-shortcuts');
    }

    /**
     * Get user preferences for export
     */
    getPreferences() {
        return {
            settings: this.settings,
            version: '1.0.0',
            exportDate: new Date().toISOString()
        };
    }

    /**
     * Import user preferences
     */
    importPreferences(preferences) {
        if (preferences.settings) {
            this.settings = { ...this.defaultSettings, ...preferences.settings };
            this.saveSettings();
            showSuccess('Preferences imported successfully');
        }
    }
}

// Create global instance
const settingsManager = new SettingsManager();
