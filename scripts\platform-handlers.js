// Platform-Specific Preview Handlers
class PlatformHandlers {
    constructor(previewService) {
        this.previewService = previewService;
    }

    /**
     * YouTube video handler
     */
    async handleYouTube(url) {
        try {
            const videoId = this.extractYouTubeVideoId(url);
            if (!videoId) throw new Error('Invalid YouTube URL');

            const config = getConfig('PREVIEW.PLATFORMS.YOUTUBE', {});
            const apiKey = getConfig('API.YOUTUBE_API_KEY');

            let preview = {
                platform: 'youtube',
                videoId: videoId,
                url: url,
                favicon: 'https://www.youtube.com/favicon.ico'
            };

            // Try YouTube API if available
            if (apiKey && this.previewService.checkRateLimit('YOUTUBE')) {
                try {
                    const apiData = await this.fetchYouTubeAPI(videoId, apiKey);
                    preview = { ...preview, ...apiData };
                } catch (error) {
                    console.warn('YouTube API failed, falling back to oEmbed:', error);
                }
            }

            // Fallback to oEmbed
            if (!preview.title) {
                const oEmbedData = await this.fetchYouTubeOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            }

            // Generate thumbnail URL
            if (!preview.image) {
                const quality = config.thumbnailQuality || 'maxresdefault';
                preview.image = `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
            }

            return preview;
        } catch (error) {
            console.error('YouTube handler error:', error);
            return null;
        }
    }

    /**
     * Twitter/X handler
     */
    async handleTwitter(url) {
        try {
            const tweetId = this.extractTwitterTweetId(url);
            if (!tweetId) throw new Error('Invalid Twitter URL');

            const config = getConfig('PREVIEW.PLATFORMS.TWITTER', {});
            const bearerToken = getConfig('API.TWITTER_BEARER_TOKEN');

            let preview = {
                platform: 'twitter',
                tweetId: tweetId,
                url: url,
                favicon: 'https://twitter.com/favicon.ico'
            };

            // Try Twitter API if available
            if (bearerToken && this.previewService.checkRateLimit('TWITTER')) {
                try {
                    const apiData = await this.fetchTwitterAPI(tweetId, bearerToken);
                    preview = { ...preview, ...apiData };
                } catch (error) {
                    console.warn('Twitter API failed, falling back to oEmbed:', error);
                }
            }

            // Fallback to oEmbed
            if (!preview.title) {
                const oEmbedData = await this.fetchTwitterOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            }

            return preview;
        } catch (error) {
            console.error('Twitter handler error:', error);
            return null;
        }
    }

    /**
     * Instagram handler
     */
    async handleInstagram(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.INSTAGRAM', {});
            
            let preview = {
                platform: 'instagram',
                url: url,
                favicon: 'https://www.instagram.com/favicon.ico'
            };

            // Try oEmbed (limited but available)
            try {
                const oEmbedData = await this.fetchInstagramOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            } catch (error) {
                console.warn('Instagram oEmbed failed:', error);
                // Fallback to basic parsing
                preview.title = 'Instagram Post';
                preview.description = 'View this post on Instagram';
                preview.siteName = 'Instagram';
            }

            return preview;
        } catch (error) {
            console.error('Instagram handler error:', error);
            return null;
        }
    }

    /**
     * TikTok handler
     */
    async handleTikTok(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.TIKTOK', {});
            
            let preview = {
                platform: 'tiktok',
                url: url,
                favicon: 'https://www.tiktok.com/favicon.ico'
            };

            // Try oEmbed
            try {
                const oEmbedData = await this.fetchTikTokOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            } catch (error) {
                console.warn('TikTok oEmbed failed:', error);
                // Basic fallback
                preview.title = 'TikTok Video';
                preview.description = 'Watch this video on TikTok';
                preview.siteName = 'TikTok';
            }

            return preview;
        } catch (error) {
            console.error('TikTok handler error:', error);
            return null;
        }
    }

    /**
     * Facebook handler
     */
    async handleFacebook(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.FACEBOOK', {});
            
            let preview = {
                platform: 'facebook',
                url: url,
                favicon: 'https://www.facebook.com/favicon.ico'
            };

            // Facebook has strict API restrictions, rely on Open Graph
            try {
                const ogData = await this.previewService.fetchManually(url);
                preview = { ...preview, ...ogData };
            } catch (error) {
                console.warn('Facebook Open Graph parsing failed:', error);
                preview.title = 'Facebook Post';
                preview.description = 'View this post on Facebook';
                preview.siteName = 'Facebook';
            }

            return preview;
        } catch (error) {
            console.error('Facebook handler error:', error);
            return null;
        }
    }

    /**
     * LinkedIn handler
     */
    async handleLinkedIn(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.LINKEDIN', {});
            
            let preview = {
                platform: 'linkedin',
                url: url,
                favicon: 'https://www.linkedin.com/favicon.ico'
            };

            // LinkedIn also relies on Open Graph
            try {
                const ogData = await this.previewService.fetchManually(url);
                preview = { ...preview, ...ogData };
            } catch (error) {
                console.warn('LinkedIn Open Graph parsing failed:', error);
                preview.title = 'LinkedIn Post';
                preview.description = 'View this post on LinkedIn';
                preview.siteName = 'LinkedIn';
            }

            return preview;
        } catch (error) {
            console.error('LinkedIn handler error:', error);
            return null;
        }
    }

    /**
     * Reddit handler
     */
    async handleReddit(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.REDDIT', {});
            
            let preview = {
                platform: 'reddit',
                url: url,
                favicon: 'https://www.reddit.com/favicon.ico'
            };

            // Try Reddit JSON API
            if (this.previewService.checkRateLimit('REDDIT')) {
                try {
                    const jsonUrl = url.replace(/\/$/, '') + '.json';
                    const response = await fetch(jsonUrl, {
                        headers: {
                            'User-Agent': 'Link Manager Pro/1.0'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const post = data[0]?.data?.children?.[0]?.data;
                        
                        if (post) {
                            preview.title = post.title;
                            preview.description = post.selftext || `Posted by u/${post.author} in r/${post.subreddit}`;
                            preview.author = `u/${post.author}`;
                            preview.subreddit = `r/${post.subreddit}`;
                            preview.score = post.score;
                            preview.comments = post.num_comments;
                            preview.publishDate = new Date(post.created_utc * 1000).toISOString();
                            
                            if (post.thumbnail && post.thumbnail !== 'self' && post.thumbnail !== 'default') {
                                preview.image = post.url;
                            }
                        }
                    }
                } catch (error) {
                    console.warn('Reddit JSON API failed:', error);
                }
            }

            // Fallback
            if (!preview.title) {
                preview.title = 'Reddit Post';
                preview.description = 'View this post on Reddit';
                preview.siteName = 'Reddit';
            }

            return preview;
        } catch (error) {
            console.error('Reddit handler error:', error);
            return null;
        }
    }

    /**
     * Enhanced Telegram handler with detailed metadata extraction
     */
    async handleTelegram(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.TELEGRAM', {});

            let preview = {
                platform: 'telegram',
                url: url,
                favicon: 'https://telegram.org/favicon.ico',
                siteName: 'Telegram'
            };

            // Parse and analyze Telegram URL
            const telegramData = this.parseTelegramUrl(url);
            if (!telegramData) {
                return this.createFallbackTelegramPreview(url);
            }

            // Try to fetch enhanced metadata
            try {
                const enhancedData = await this.fetchTelegramMetadata(telegramData, config);
                preview = { ...preview, ...enhancedData };
            } catch (error) {
                console.warn('Enhanced Telegram metadata fetch failed:', error);
                // Use basic parsed data as fallback
                preview = { ...preview, ...this.createBasicTelegramPreview(telegramData) };
            }

            return preview;
        } catch (error) {
            console.error('Telegram handler error:', error);
            return this.createFallbackTelegramPreview(url);
        }
    }

    /**
     * Parse Telegram URL and extract components
     */
    parseTelegramUrl(url) {
        try {
            const urlObj = new URL(url);

            // Handle different Telegram URL formats
            if (!urlObj.hostname.includes('t.me') && !urlObj.hostname.includes('telegram.me')) {
                return null;
            }

            const pathParts = urlObj.pathname.split('/').filter(p => p);

            if (pathParts.length === 0) {
                return null;
            }

            const result = {
                type: 'unknown',
                username: pathParts[0],
                originalUrl: url
            };

            // Determine link type and extract relevant information
            if (pathParts.length === 1) {
                // Channel, group, or bot link: t.me/username
                result.type = this.determineTelegramEntityType(pathParts[0]);
                result.displayName = this.formatTelegramUsername(pathParts[0]);
            } else if (pathParts.length >= 2) {
                // Message link: t.me/username/messageId or t.me/c/channelId/messageId
                if (pathParts[0] === 'c' && pathParts.length >= 3) {
                    // Private channel message: t.me/c/channelId/messageId
                    result.type = 'private_channel_message';
                    result.channelId = pathParts[1];
                    result.messageId = pathParts[2];
                    result.displayName = 'Private Channel';
                } else {
                    // Public channel/group message: t.me/username/messageId
                    result.type = 'message';
                    result.messageId = pathParts[1];
                    result.displayName = this.formatTelegramUsername(pathParts[0]);

                    // Check for additional parameters
                    if (pathParts.length > 2) {
                        result.threadId = pathParts[2];
                        result.type = 'thread_message';
                    }
                }
            }

            // Extract URL parameters for additional context
            const params = new URLSearchParams(urlObj.search);
            if (params.has('start')) {
                result.startParam = params.get('start');
                result.type = 'bot_start';
            }
            if (params.has('startgroup')) {
                result.startGroupParam = params.get('startgroup');
                result.type = 'bot_add_to_group';
            }

            return result;
        } catch (error) {
            console.error('Error parsing Telegram URL:', error);
            return null;
        }
    }

    /**
     * Determine the type of Telegram entity based on username patterns
     */
    determineTelegramEntityType(username) {
        // Bot usernames typically end with 'bot'
        if (username.toLowerCase().endsWith('bot')) {
            return 'bot';
        }

        // Check for common channel/group naming patterns
        if (username.includes('channel') || username.includes('news')) {
            return 'channel';
        }

        if (username.includes('group') || username.includes('chat')) {
            return 'group';
        }

        // Default to channel for public entities
        return 'channel';
    }

    /**
     * Format Telegram username for display
     */
    formatTelegramUsername(username) {
        // Remove @ if present and add it back for consistency
        const cleanUsername = username.replace(/^@/, '');
        return `@${cleanUsername}`;
    }

    /**
     * Attempt to fetch enhanced metadata from various sources
     */
    async fetchTelegramMetadata(telegramData, config) {
        const preview = {};

        // Try multiple approaches to get enhanced metadata
        try {
            // Approach 1: Try to fetch from Telegram's public preview API (if available)
            const webPreview = await this.fetchTelegramWebPreview(telegramData);
            if (webPreview) {
                Object.assign(preview, webPreview);
            }
        } catch (error) {
            console.warn('Telegram web preview failed:', error);
        }

        try {
            // Approach 2: Try to extract from Open Graph meta tags
            const ogData = await this.fetchTelegramOpenGraph(telegramData.originalUrl);
            if (ogData) {
                Object.assign(preview, ogData);
            }
        } catch (error) {
            console.warn('Telegram Open Graph fetch failed:', error);
        }

        // If we still don't have good data, create enhanced basic preview
        if (!preview.title) {
            Object.assign(preview, this.createEnhancedTelegramPreview(telegramData));
        }

        return preview;
    }

    /**
     * Attempt to fetch Telegram web preview data
     */
    async fetchTelegramWebPreview(telegramData) {
        // Note: This is a placeholder for potential future Telegram API integration
        // Currently, Telegram doesn't provide a public API for this purpose

        // We could potentially scrape the web version, but this would be against ToS
        // Instead, we'll return null and rely on other methods
        return null;
    }

    /**
     * Fetch Open Graph data from Telegram URL
     */
    async fetchTelegramOpenGraph(url) {
        try {
            // Use the main preview service to fetch Open Graph data
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (compatible; LinkManagerPro/1.0)'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const html = await response.text();
            return this.parseOpenGraphFromHtml(html);
        } catch (error) {
            console.warn('Failed to fetch Telegram Open Graph data:', error);
            return null;
        }
    }

    /**
     * Parse Open Graph meta tags from HTML
     */
    parseOpenGraphFromHtml(html) {
        const ogData = {};

        // Extract Open Graph meta tags
        const ogRegex = /<meta\s+property="og:([^"]+)"\s+content="([^"]+)"/gi;
        let match;

        while ((match = ogRegex.exec(html)) !== null) {
            const property = match[1];
            const content = match[2];

            switch (property) {
                case 'title':
                    ogData.title = this.decodeHtmlEntities(content);
                    break;
                case 'description':
                    ogData.description = this.decodeHtmlEntities(content);
                    break;
                case 'image':
                    ogData.image = content;
                    break;
                case 'site_name':
                    ogData.siteName = content;
                    break;
            }
        }

        // Also try standard meta tags
        const titleMatch = html.match(/<title>([^<]+)<\/title>/i);
        if (titleMatch && !ogData.title) {
            ogData.title = this.decodeHtmlEntities(titleMatch[1]);
        }

        const descMatch = html.match(/<meta\s+name="description"\s+content="([^"]+)"/i);
        if (descMatch && !ogData.description) {
            ogData.description = this.decodeHtmlEntities(descMatch[1]);
        }

        return Object.keys(ogData).length > 0 ? ogData : null;
    }

    /**
     * Create enhanced preview based on parsed Telegram data
     */
    createEnhancedTelegramPreview(telegramData) {
        const preview = {};

        switch (telegramData.type) {
            case 'bot':
                preview.title = `${telegramData.displayName} Bot`;
                preview.description = `Telegram bot - Click to start chatting or add to group`;
                preview.entityType = 'bot';
                break;

            case 'channel':
                preview.title = `${telegramData.displayName}`;
                preview.description = `Telegram channel - Join to receive updates and content`;
                preview.entityType = 'channel';
                break;

            case 'group':
                preview.title = `${telegramData.displayName}`;
                preview.description = `Telegram group - Join the conversation`;
                preview.entityType = 'group';
                break;

            case 'message':
                preview.title = `Message in ${telegramData.displayName}`;
                preview.description = `View this message on Telegram`;
                preview.entityType = 'message';
                preview.messageId = telegramData.messageId;
                break;

            case 'thread_message':
                preview.title = `Thread in ${telegramData.displayName}`;
                preview.description = `View this message thread on Telegram`;
                preview.entityType = 'thread';
                preview.messageId = telegramData.messageId;
                preview.threadId = telegramData.threadId;
                break;

            case 'private_channel_message':
                preview.title = `Private Channel Message`;
                preview.description = `View this message on Telegram (private channel)`;
                preview.entityType = 'private_message';
                preview.messageId = telegramData.messageId;
                break;

            case 'bot_start':
                preview.title = `${telegramData.displayName} Bot`;
                preview.description = `Start bot with parameter: ${telegramData.startParam}`;
                preview.entityType = 'bot_start';
                preview.startParam = telegramData.startParam;
                break;

            case 'bot_add_to_group':
                preview.title = `Add ${telegramData.displayName} to Group`;
                preview.description = `Add this bot to your group chat`;
                preview.entityType = 'bot_group';
                break;

            default:
                preview.title = telegramData.displayName || 'Telegram Link';
                preview.description = 'View on Telegram';
                preview.entityType = 'unknown';
        }

        // Add common metadata
        preview.username = telegramData.username;
        preview.telegramType = telegramData.type;

        return preview;
    }

    /**
     * Create basic Telegram preview from parsed data
     */
    createBasicTelegramPreview(telegramData) {
        return {
            title: telegramData.displayName || `@${telegramData.username}`,
            description: `View on Telegram`,
            username: telegramData.username,
            telegramType: telegramData.type
        };
    }

    /**
     * Create fallback preview for invalid or unparseable Telegram URLs
     */
    createFallbackTelegramPreview(url) {
        return {
            platform: 'telegram',
            title: 'Telegram Link',
            description: 'View on Telegram',
            url: url,
            favicon: 'https://telegram.org/favicon.ico',
            siteName: 'Telegram',
            entityType: 'unknown'
        };
    }

    /**
     * Decode HTML entities
     */
    decodeHtmlEntities(text) {
        const textarea = document.createElement('textarea');
        textarea.innerHTML = text;
        return textarea.value;
    }

    // Utility methods for extracting IDs and fetching data

    extractYouTubeVideoId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
            /youtube\.com\/v\/([^&\n?#]+)/
        ];
        
        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) return match[1];
        }
        return null;
    }

    extractTwitterTweetId(url) {
        const match = url.match(/(?:twitter\.com|x\.com)\/\w+\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    async fetchYouTubeAPI(videoId, apiKey) {
        const url = `${getConfig('API.YOUTUBE_API_BASE')}/videos?id=${videoId}&key=${apiKey}&part=snippet,statistics,contentDetails`;
        
        const response = await fetch(url);
        if (!response.ok) throw new Error('YouTube API request failed');
        
        const data = await response.json();
        const video = data.items?.[0];
        
        if (!video) throw new Error('Video not found');
        
        return {
            title: video.snippet.title,
            description: video.snippet.description,
            author: video.snippet.channelTitle,
            publishDate: video.snippet.publishedAt,
            duration: this.parseYouTubeDuration(video.contentDetails.duration),
            viewCount: parseInt(video.statistics.viewCount),
            likeCount: parseInt(video.statistics.likeCount),
            commentCount: parseInt(video.statistics.commentCount),
            siteName: 'YouTube'
        };
    }

    async fetchYouTubeOEmbed(url) {
        const oEmbedUrl = `https://www.youtube.com/oembed?url=${encodeURIComponent(url)}&format=json`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('YouTube oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title,
            author: data.author_name,
            siteName: 'YouTube'
        };
    }

    async fetchTwitterOEmbed(url) {
        const oEmbedUrl = `https://publish.twitter.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('Twitter oEmbed failed');
        
        const data = await response.json();
        return {
            title: 'Tweet',
            description: data.html?.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
            author: data.author_name,
            siteName: 'Twitter'
        };
    }

    async fetchInstagramOEmbed(url) {
        const oEmbedUrl = `https://api.instagram.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('Instagram oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title || 'Instagram Post',
            author: data.author_name,
            siteName: 'Instagram'
        };
    }

    async fetchTikTokOEmbed(url) {
        const oEmbedUrl = `https://www.tiktok.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('TikTok oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title || 'TikTok Video',
            author: data.author_name,
            siteName: 'TikTok'
        };
    }

    parseYouTubeDuration(duration) {
        const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
        if (!match) return null;
        
        const hours = parseInt(match[1]) || 0;
        const minutes = parseInt(match[2]) || 0;
        const seconds = parseInt(match[3]) || 0;
        
        return hours * 3600 + minutes * 60 + seconds;
    }
}
