// Platform-Specific Preview Handlers
class PlatformHandlers {
    constructor(previewService) {
        this.previewService = previewService;
    }

    /**
     * YouTube video handler
     */
    async handleYouTube(url) {
        try {
            const videoId = this.extractYouTubeVideoId(url);
            if (!videoId) throw new Error('Invalid YouTube URL');

            const config = getConfig('PREVIEW.PLATFORMS.YOUTUBE', {});
            const apiKey = getConfig('API.YOUTUBE_API_KEY');

            let preview = {
                platform: 'youtube',
                videoId: videoId,
                url: url,
                favicon: 'https://www.youtube.com/favicon.ico'
            };

            // Try YouTube API if available
            if (apiKey && this.previewService.checkRateLimit('YOUTUBE')) {
                try {
                    const apiData = await this.fetchYouTubeAPI(videoId, apiKey);
                    preview = { ...preview, ...apiData };
                } catch (error) {
                    console.warn('YouTube API failed, falling back to oEmbed:', error);
                }
            }

            // Fallback to oEmbed
            if (!preview.title) {
                const oEmbedData = await this.fetchYouTubeOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            }

            // Generate thumbnail URL
            if (!preview.image) {
                const quality = config.thumbnailQuality || 'maxresdefault';
                preview.image = `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
            }

            return preview;
        } catch (error) {
            console.error('YouTube handler error:', error);
            return null;
        }
    }

    /**
     * Twitter/X handler
     */
    async handleTwitter(url) {
        try {
            const tweetId = this.extractTwitterTweetId(url);
            if (!tweetId) throw new Error('Invalid Twitter URL');

            const config = getConfig('PREVIEW.PLATFORMS.TWITTER', {});
            const bearerToken = getConfig('API.TWITTER_BEARER_TOKEN');

            let preview = {
                platform: 'twitter',
                tweetId: tweetId,
                url: url,
                favicon: 'https://twitter.com/favicon.ico'
            };

            // Try Twitter API if available
            if (bearerToken && this.previewService.checkRateLimit('TWITTER')) {
                try {
                    const apiData = await this.fetchTwitterAPI(tweetId, bearerToken);
                    preview = { ...preview, ...apiData };
                } catch (error) {
                    console.warn('Twitter API failed, falling back to oEmbed:', error);
                }
            }

            // Fallback to oEmbed
            if (!preview.title) {
                const oEmbedData = await this.fetchTwitterOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            }

            return preview;
        } catch (error) {
            console.error('Twitter handler error:', error);
            return null;
        }
    }

    /**
     * Instagram handler
     */
    async handleInstagram(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.INSTAGRAM', {});
            
            let preview = {
                platform: 'instagram',
                url: url,
                favicon: 'https://www.instagram.com/favicon.ico'
            };

            // Try oEmbed (limited but available)
            try {
                const oEmbedData = await this.fetchInstagramOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            } catch (error) {
                console.warn('Instagram oEmbed failed:', error);
                // Fallback to basic parsing
                preview.title = 'Instagram Post';
                preview.description = 'View this post on Instagram';
                preview.siteName = 'Instagram';
            }

            return preview;
        } catch (error) {
            console.error('Instagram handler error:', error);
            return null;
        }
    }

    /**
     * TikTok handler
     */
    async handleTikTok(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.TIKTOK', {});
            
            let preview = {
                platform: 'tiktok',
                url: url,
                favicon: 'https://www.tiktok.com/favicon.ico'
            };

            // Try oEmbed
            try {
                const oEmbedData = await this.fetchTikTokOEmbed(url);
                preview = { ...preview, ...oEmbedData };
            } catch (error) {
                console.warn('TikTok oEmbed failed:', error);
                // Basic fallback
                preview.title = 'TikTok Video';
                preview.description = 'Watch this video on TikTok';
                preview.siteName = 'TikTok';
            }

            return preview;
        } catch (error) {
            console.error('TikTok handler error:', error);
            return null;
        }
    }

    /**
     * Facebook handler
     */
    async handleFacebook(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.FACEBOOK', {});
            
            let preview = {
                platform: 'facebook',
                url: url,
                favicon: 'https://www.facebook.com/favicon.ico'
            };

            // Facebook has strict API restrictions, rely on Open Graph
            try {
                const ogData = await this.previewService.fetchManually(url);
                preview = { ...preview, ...ogData };
            } catch (error) {
                console.warn('Facebook Open Graph parsing failed:', error);
                preview.title = 'Facebook Post';
                preview.description = 'View this post on Facebook';
                preview.siteName = 'Facebook';
            }

            return preview;
        } catch (error) {
            console.error('Facebook handler error:', error);
            return null;
        }
    }

    /**
     * LinkedIn handler
     */
    async handleLinkedIn(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.LINKEDIN', {});
            
            let preview = {
                platform: 'linkedin',
                url: url,
                favicon: 'https://www.linkedin.com/favicon.ico'
            };

            // LinkedIn also relies on Open Graph
            try {
                const ogData = await this.previewService.fetchManually(url);
                preview = { ...preview, ...ogData };
            } catch (error) {
                console.warn('LinkedIn Open Graph parsing failed:', error);
                preview.title = 'LinkedIn Post';
                preview.description = 'View this post on LinkedIn';
                preview.siteName = 'LinkedIn';
            }

            return preview;
        } catch (error) {
            console.error('LinkedIn handler error:', error);
            return null;
        }
    }

    /**
     * Reddit handler
     */
    async handleReddit(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.REDDIT', {});
            
            let preview = {
                platform: 'reddit',
                url: url,
                favicon: 'https://www.reddit.com/favicon.ico'
            };

            // Try Reddit JSON API
            if (this.previewService.checkRateLimit('REDDIT')) {
                try {
                    const jsonUrl = url.replace(/\/$/, '') + '.json';
                    const response = await fetch(jsonUrl, {
                        headers: {
                            'User-Agent': 'Link Manager Pro/1.0'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const post = data[0]?.data?.children?.[0]?.data;
                        
                        if (post) {
                            preview.title = post.title;
                            preview.description = post.selftext || `Posted by u/${post.author} in r/${post.subreddit}`;
                            preview.author = `u/${post.author}`;
                            preview.subreddit = `r/${post.subreddit}`;
                            preview.score = post.score;
                            preview.comments = post.num_comments;
                            preview.publishDate = new Date(post.created_utc * 1000).toISOString();
                            
                            if (post.thumbnail && post.thumbnail !== 'self' && post.thumbnail !== 'default') {
                                preview.image = post.url;
                            }
                        }
                    }
                } catch (error) {
                    console.warn('Reddit JSON API failed:', error);
                }
            }

            // Fallback
            if (!preview.title) {
                preview.title = 'Reddit Post';
                preview.description = 'View this post on Reddit';
                preview.siteName = 'Reddit';
            }

            return preview;
        } catch (error) {
            console.error('Reddit handler error:', error);
            return null;
        }
    }

    /**
     * Telegram handler
     */
    async handleTelegram(url) {
        try {
            const config = getConfig('PREVIEW.PLATFORMS.TELEGRAM', {});
            
            let preview = {
                platform: 'telegram',
                url: url,
                favicon: 'https://telegram.org/favicon.ico'
            };

            // Parse Telegram URL patterns
            const urlObj = new URL(url);
            const pathParts = urlObj.pathname.split('/').filter(p => p);
            
            if (pathParts.length > 0) {
                const channelOrBot = pathParts[0];
                
                if (pathParts.length === 1) {
                    // Channel or bot link
                    preview.title = `@${channelOrBot}`;
                    preview.description = `Telegram channel or bot`;
                    preview.channelName = channelOrBot;
                } else if (pathParts.length >= 2) {
                    // Message link
                    const messageId = pathParts[1];
                    preview.title = `Message in @${channelOrBot}`;
                    preview.description = `View this message on Telegram`;
                    preview.channelName = channelOrBot;
                    preview.messageId = messageId;
                }
            }

            preview.siteName = 'Telegram';
            return preview;
        } catch (error) {
            console.error('Telegram handler error:', error);
            return null;
        }
    }

    // Utility methods for extracting IDs and fetching data

    extractYouTubeVideoId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
            /youtube\.com\/v\/([^&\n?#]+)/
        ];
        
        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) return match[1];
        }
        return null;
    }

    extractTwitterTweetId(url) {
        const match = url.match(/(?:twitter\.com|x\.com)\/\w+\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    async fetchYouTubeAPI(videoId, apiKey) {
        const url = `${getConfig('API.YOUTUBE_API_BASE')}/videos?id=${videoId}&key=${apiKey}&part=snippet,statistics,contentDetails`;
        
        const response = await fetch(url);
        if (!response.ok) throw new Error('YouTube API request failed');
        
        const data = await response.json();
        const video = data.items?.[0];
        
        if (!video) throw new Error('Video not found');
        
        return {
            title: video.snippet.title,
            description: video.snippet.description,
            author: video.snippet.channelTitle,
            publishDate: video.snippet.publishedAt,
            duration: this.parseYouTubeDuration(video.contentDetails.duration),
            viewCount: parseInt(video.statistics.viewCount),
            likeCount: parseInt(video.statistics.likeCount),
            commentCount: parseInt(video.statistics.commentCount),
            siteName: 'YouTube'
        };
    }

    async fetchYouTubeOEmbed(url) {
        const oEmbedUrl = `https://www.youtube.com/oembed?url=${encodeURIComponent(url)}&format=json`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('YouTube oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title,
            author: data.author_name,
            siteName: 'YouTube'
        };
    }

    async fetchTwitterOEmbed(url) {
        const oEmbedUrl = `https://publish.twitter.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('Twitter oEmbed failed');
        
        const data = await response.json();
        return {
            title: 'Tweet',
            description: data.html?.replace(/<[^>]*>/g, '').substring(0, 200) + '...',
            author: data.author_name,
            siteName: 'Twitter'
        };
    }

    async fetchInstagramOEmbed(url) {
        const oEmbedUrl = `https://api.instagram.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('Instagram oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title || 'Instagram Post',
            author: data.author_name,
            siteName: 'Instagram'
        };
    }

    async fetchTikTokOEmbed(url) {
        const oEmbedUrl = `https://www.tiktok.com/oembed?url=${encodeURIComponent(url)}`;
        
        const response = await fetch(oEmbedUrl);
        if (!response.ok) throw new Error('TikTok oEmbed failed');
        
        const data = await response.json();
        return {
            title: data.title || 'TikTok Video',
            author: data.author_name,
            siteName: 'TikTok'
        };
    }

    parseYouTubeDuration(duration) {
        const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
        if (!match) return null;
        
        const hours = parseInt(match[1]) || 0;
        const minutes = parseInt(match[2]) || 0;
        const seconds = parseInt(match[3]) || 0;
        
        return hours * 3600 + minutes * 60 + seconds;
    }
}
