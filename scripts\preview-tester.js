// Preview Testing Utility for Development and Debugging
class PreviewTester {
    constructor() {
        this.testUrls = {
            youtube: [
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'https://youtu.be/dQw4w9WgXcQ',
                'https://www.youtube.com/watch?v=jNQXAC9IVRw'
            ],
            twitter: [
                'https://twitter.com/elonmusk/status/1234567890',
                'https://x.com/twitter/status/1234567890'
            ],
            instagram: [
                'https://www.instagram.com/p/ABC123/',
                'https://www.instagram.com/reel/ABC123/'
            ],
            tiktok: [
                'https://www.tiktok.com/@user/video/1234567890',
                'https://vm.tiktok.com/ABC123/'
            ],
            reddit: [
                'https://www.reddit.com/r/programming/comments/abc123/title/',
                'https://reddit.com/r/webdev/comments/xyz789/post/'
            ],
            linkedin: [
                'https://www.linkedin.com/posts/username_activity-123456789',
                'https://www.linkedin.com/pulse/article-title-author'
            ],
            facebook: [
                'https://www.facebook.com/user/posts/123456789',
                'https://www.facebook.com/watch/?v=123456789'
            ],
            telegram: [
                'https://t.me/channel',
                'https://t.me/channel/123'
            ],
            generic: [
                'https://github.com/microsoft/vscode',
                'https://stackoverflow.com/questions/123456/title',
                'https://medium.com/@author/article-title-123'
            ]
        };
    }

    /**
     * Test all platform handlers
     */
    async testAllPlatforms() {
        console.log('🧪 Starting comprehensive preview testing...');
        
        const results = {};
        
        for (const [platform, urls] of Object.entries(this.testUrls)) {
            console.log(`\n📱 Testing ${platform.toUpperCase()} platform...`);
            results[platform] = await this.testPlatform(platform, urls);
        }
        
        this.displayResults(results);
        return results;
    }

    /**
     * Test specific platform
     */
    async testPlatform(platform, urls) {
        const results = [];
        
        for (const url of urls) {
            console.log(`  🔗 Testing: ${url}`);
            
            try {
                const startTime = Date.now();
                const preview = await previewService.getPreview(url, false); // Force fresh fetch
                const duration = Date.now() - startTime;
                
                results.push({
                    url,
                    success: true,
                    preview,
                    duration,
                    platform: preview?.platform || 'unknown'
                });
                
                console.log(`    ✅ Success (${duration}ms) - ${preview?.title || 'No title'}`);
                
            } catch (error) {
                results.push({
                    url,
                    success: false,
                    error: error.message,
                    duration: null
                });
                
                console.log(`    ❌ Failed - ${error.message}`);
            }
        }
        
        return results;
    }

    /**
     * Test specific URL
     */
    async testUrl(url) {
        console.log(`🔍 Testing single URL: ${url}`);
        
        try {
            const startTime = Date.now();
            const preview = await previewService.getPreview(url, false);
            const duration = Date.now() - startTime;
            
            console.log('✅ Preview result:', preview);
            console.log(`⏱️ Duration: ${duration}ms`);
            
            return { success: true, preview, duration };
            
        } catch (error) {
            console.error('❌ Preview failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Display comprehensive test results
     */
    displayResults(results) {
        console.log('\n📊 PREVIEW TESTING RESULTS');
        console.log('=' .repeat(50));
        
        let totalTests = 0;
        let successfulTests = 0;
        let totalDuration = 0;
        
        for (const [platform, platformResults] of Object.entries(results)) {
            const successful = platformResults.filter(r => r.success).length;
            const total = platformResults.length;
            const avgDuration = platformResults
                .filter(r => r.duration)
                .reduce((sum, r) => sum + r.duration, 0) / successful || 0;
            
            console.log(`\n${platform.toUpperCase()}:`);
            console.log(`  Success Rate: ${successful}/${total} (${(successful/total*100).toFixed(1)}%)`);
            console.log(`  Avg Duration: ${avgDuration.toFixed(0)}ms`);
            
            totalTests += total;
            successfulTests += successful;
            totalDuration += avgDuration * successful;
        }
        
        console.log('\n📈 OVERALL STATISTICS:');
        console.log(`  Total Tests: ${totalTests}`);
        console.log(`  Success Rate: ${successfulTests}/${totalTests} (${(successfulTests/totalTests*100).toFixed(1)}%)`);
        console.log(`  Avg Duration: ${(totalDuration/successfulTests).toFixed(0)}ms`);
        
        // Show cache statistics
        const cacheSize = previewService.getCacheSize();
        console.log(`  Cache Size: ${cacheSize} items`);
    }

    /**
     * Test rate limiting
     */
    async testRateLimiting() {
        console.log('🚦 Testing rate limiting...');
        
        const testUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
        const requests = [];
        
        // Make multiple rapid requests
        for (let i = 0; i < 10; i++) {
            requests.push(this.testUrl(testUrl));
        }
        
        const results = await Promise.allSettled(requests);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
        
        console.log(`Rate limiting test: ${successful}/10 requests succeeded`);
        
        return { successful, total: 10 };
    }

    /**
     * Test cache functionality
     */
    async testCache() {
        console.log('💾 Testing cache functionality...');
        
        const testUrl = 'https://github.com/microsoft/vscode';
        
        // Clear cache first
        previewService.clearCache();
        console.log('  Cache cleared');
        
        // First request (should fetch)
        const start1 = Date.now();
        await previewService.getPreview(testUrl, false);
        const duration1 = Date.now() - start1;
        console.log(`  First request: ${duration1}ms (fresh fetch)`);
        
        // Second request (should use cache)
        const start2 = Date.now();
        await previewService.getPreview(testUrl, true);
        const duration2 = Date.now() - start2;
        console.log(`  Second request: ${duration2}ms (cached)`);
        
        const speedup = duration1 / duration2;
        console.log(`  Cache speedup: ${speedup.toFixed(1)}x faster`);
        
        return { freshDuration: duration1, cachedDuration: duration2, speedup };
    }

    /**
     * Test error handling
     */
    async testErrorHandling() {
        console.log('🚨 Testing error handling...');
        
        const invalidUrls = [
            'not-a-url',
            'https://this-domain-does-not-exist-12345.com',
            'https://httpstat.us/404',
            'https://httpstat.us/500'
        ];
        
        const results = [];
        
        for (const url of invalidUrls) {
            try {
                const preview = await previewService.getPreview(url);
                results.push({ url, success: true, preview });
                console.log(`  ✅ ${url} - Handled gracefully`);
            } catch (error) {
                results.push({ url, success: false, error: error.message });
                console.log(`  ❌ ${url} - ${error.message}`);
            }
        }
        
        return results;
    }

    /**
     * Performance benchmark
     */
    async benchmark() {
        console.log('⚡ Running performance benchmark...');
        
        const urls = [
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://github.com/microsoft/vscode',
            'https://stackoverflow.com/questions/123456',
            'https://www.reddit.com/r/programming',
            'https://medium.com/@author/article'
        ];
        
        // Sequential requests
        const sequentialStart = Date.now();
        for (const url of urls) {
            await previewService.getPreview(url, false);
        }
        const sequentialDuration = Date.now() - sequentialStart;
        
        // Clear cache
        previewService.clearCache();
        
        // Parallel requests
        const parallelStart = Date.now();
        await Promise.all(urls.map(url => previewService.getPreview(url, false)));
        const parallelDuration = Date.now() - parallelStart;
        
        console.log(`  Sequential: ${sequentialDuration}ms`);
        console.log(`  Parallel: ${parallelDuration}ms`);
        console.log(`  Speedup: ${(sequentialDuration / parallelDuration).toFixed(1)}x`);
        
        return { sequentialDuration, parallelDuration };
    }

    /**
     * Generate test report
     */
    async generateReport() {
        console.log('📋 Generating comprehensive test report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            tests: {}
        };
        
        // Run all tests
        report.tests.platforms = await this.testAllPlatforms();
        report.tests.rateLimiting = await this.testRateLimiting();
        report.tests.cache = await this.testCache();
        report.tests.errorHandling = await this.testErrorHandling();
        report.tests.performance = await this.benchmark();
        
        // Save report
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `preview-test-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📄 Test report saved');
        return report;
    }
}

// Global instance for console access
window.previewTester = new PreviewTester();

// Console helper functions
window.testPreviews = () => window.previewTester.testAllPlatforms();
window.testUrl = (url) => window.previewTester.testUrl(url);
window.testCache = () => window.previewTester.testCache();
window.generatePreviewReport = () => window.previewTester.generateReport();
