{"exportDate": "2024-01-15T10:30:00.000Z", "version": "1.0.0", "format": "Link Manager Pro Export", "data": {"links": [{"id": "sample-1", "title": "GitHub - The world's leading software development platform", "url": "https://github.com", "description": "GitHub is where over 100 million developers shape the future of software, together. Contribute to the open source community, manage Git repositories, and review code.", "category": "work", "tags": ["development", "git", "open-source", "collaboration"], "favorite": true, "createdAt": "2024-01-10T09:15:00.000Z", "updatedAt": "2024-01-10T09:15:00.000Z", "clickCount": 15, "preview": {"title": "GitHub - The world's leading software development platform", "description": "GitHub is where over 100 million developers shape the future of software, together.", "image": "https://github.githubassets.com/images/modules/site/social-cards/github-social.png", "siteName": "GitHub", "favicon": "https://github.com/favicon.ico"}}, {"id": "sample-2", "title": "Stack Overflow - Where Developers Learn, Share, & Build Careers", "url": "https://stackoverflow.com", "description": "Stack Overflow is the largest, most trusted online community for developers to learn, share their programming knowledge, and build their careers.", "category": "learning", "tags": ["programming", "questions", "community", "help"], "favorite": false, "createdAt": "2024-01-12T14:22:00.000Z", "updatedAt": "2024-01-12T14:22:00.000Z", "clickCount": 8, "preview": {"title": "Stack Overflow - Where Developers Learn, Share, & Build Careers", "description": "Stack Overflow is the largest, most trusted online community for developers.", "image": "https://cdn.sstatic.net/Sites/stackoverflow/Img/<EMAIL>", "siteName": "Stack Overflow", "favicon": "https://stackoverflow.com/favicon.ico"}}, {"id": "sample-3", "title": "MDN Web Docs", "url": "https://developer.mozilla.org", "description": "The MDN Web Docs site provides information about Open Web technologies including HTML, CSS, and APIs for both Web sites and progressive web apps.", "category": "learning", "tags": ["documentation", "web", "html", "css", "javascript"], "favorite": true, "createdAt": "2024-01-08T16:45:00.000Z", "updatedAt": "2024-01-08T16:45:00.000Z", "clickCount": 23, "preview": {"title": "MDN Web Docs", "description": "The MDN Web Docs site provides information about Open Web technologies.", "image": "https://developer.mozilla.org/mdn-social-share.png", "siteName": "MDN Web Docs", "favicon": "https://developer.mozilla.org/favicon.ico"}}, {"id": "sample-4", "title": "Figma: The Collaborative Interface Design Tool", "url": "https://figma.com", "description": "Figma is a collaborative web application for interface design, with additional offline features enabled by desktop applications for macOS and Windows.", "category": "tools", "tags": ["design", "ui", "collaboration", "prototyping"], "favorite": false, "createdAt": "2024-01-14T11:30:00.000Z", "updatedAt": "2024-01-14T11:30:00.000Z", "clickCount": 5, "preview": {"title": "Figma: The Collaborative Interface Design Tool", "description": "Figma is a collaborative web application for interface design.", "image": "https://cdn.sanity.io/images/599r6htc/localized/46a76c802176eb17b04e12108de7e7e0f3736dc6-1024x1024.png", "siteName": "Figma", "favicon": "https://static.figma.com/app/icon/1/favicon.ico"}}, {"id": "sample-5", "title": "Netflix", "url": "https://netflix.com", "description": "Watch Netflix movies & TV shows online or stream right to your smart TV, game console, PC, Mac, mobile, tablet and more.", "category": "entertainment", "tags": ["streaming", "movies", "tv-shows", "entertainment"], "favorite": true, "createdAt": "2024-01-13T20:15:00.000Z", "updatedAt": "2024-01-13T20:15:00.000Z", "clickCount": 12, "preview": {"title": "Netflix", "description": "Watch Netflix movies & TV shows online or stream right to your devices.", "image": "https://assets.nflxext.com/ffe/siteui/common/icons/nficon2016.png", "siteName": "Netflix", "favicon": "https://assets.nflxext.com/us/ffe/siteui/common/icons/nficon2016.ico"}}, {"id": "sample-6", "title": "Notion – The all-in-one workspace", "url": "https://notion.so", "description": "A new tool that blends your everyday work apps into one. It's the all-in-one workspace for you and your team.", "category": "tools", "tags": ["productivity", "notes", "workspace", "collaboration"], "favorite": false, "createdAt": "2024-01-11T13:20:00.000Z", "updatedAt": "2024-01-11T13:20:00.000Z", "clickCount": 7, "preview": {"title": "Notion – The all-in-one workspace", "description": "A new tool that blends your everyday work apps into one.", "image": "https://www.notion.so/images/meta/default.png", "siteName": "Notion", "favicon": "https://www.notion.so/images/favicon.ico"}}], "categories": [{"id": "all", "name": "All Links", "icon": "fas fa-globe", "color": "#6366f1", "linkCount": 6}, {"id": "work", "name": "Work", "icon": "fas fa-briefcase", "color": "#059669", "createdAt": "2024-01-01T00:00:00.000Z", "linkCount": 1}, {"id": "personal", "name": "Personal", "icon": "fas fa-user", "color": "#dc2626", "createdAt": "2024-01-01T00:00:00.000Z", "linkCount": 0}, {"id": "learning", "name": "Learning", "icon": "fas fa-graduation-cap", "color": "#7c3aed", "createdAt": "2024-01-01T00:00:00.000Z", "linkCount": 2}, {"id": "entertainment", "name": "Entertainment", "icon": "fas fa-play", "color": "#ea580c", "createdAt": "2024-01-01T00:00:00.000Z", "linkCount": 1}, {"id": "tools", "name": "Tools", "icon": "fas fa-tools", "color": "#0891b2", "createdAt": "2024-01-01T00:00:00.000Z", "linkCount": 2}, {"id": "favorites", "name": "Favorites", "icon": "fas fa-star", "color": "#ca8a04", "linkCount": 3}], "settings": {"theme": "dark", "viewMode": "grid", "autoPreview": true, "notifications": true, "keyboardShortcuts": true, "autoBackup": true, "linksPerPage": 12, "defaultCategory": "", "language": "en", "dateFormat": "relative", "openLinksInNewTab": true, "showLinkPreviews": true, "compactMode": false, "animationsEnabled": true, "version": "1.0.0", "firstRun": false, "createdAt": "2024-01-01T00:00:00.000Z"}}}