// Main Application Logic
class LinkManagerApp {
    constructor() {
        this.links = [];
        this.filteredLinks = [];
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.domainFilter = '';
        this.tagFilter = '';
        this.isLoading = false;
        this.draggedElement = null;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            // Load data
            await this.loadData();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initial render
            this.render();
            
            // Show welcome message for first-time users
            this.showWelcomeMessage();
            
            console.log('Link Manager Pro initialized successfully');
        } catch (error) {
            console.error('Failed to initialize app:', error);
            showError('Failed to initialize application');
        }
    }

    /**
     * Load data from storage
     */
    async loadData() {
        this.links = storageManager.get(storageManager.keys.LINKS, []);
        this.filteredLinks = [...this.links];
        
        // Update category link counts
        categoryManager.updateLinkCounts();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add link buttons
        document.getElementById('addLinkBtn')?.addEventListener('click', () => {
            this.showAddLinkModal();
        });

        document.getElementById('addFirstLinkBtn')?.addEventListener('click', () => {
            this.showAddLinkModal();
        });

        // View toggle button
        document.getElementById('viewToggleBtn')?.addEventListener('click', () => {
            this.toggleViewMode();
        });

        // Search functionality
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.toggleSearch();
        });

        document.getElementById('searchInput')?.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // Advanced filters
        document.getElementById('domainFilter')?.addEventListener('change', (e) => {
            this.handleDomainFilter(e.target.value);
        });

        document.getElementById('tagFilter')?.addEventListener('change', (e) => {
            this.handleTagFilter(e.target.value);
        });

        document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
            this.clearAllFilters();
        });

        // Floating action button (mobile)
        document.getElementById('fabAddLink')?.addEventListener('click', () => {
            this.showAddLinkModal();
        });

        // Category changes
        window.addEventListener('category-changed', (e) => {
            this.handleCategoryChange(e.detail.categoryId);
        });

        // Link events
        window.addEventListener('link-add', (e) => {
            this.addLink(e.detail.data);
        });

        window.addEventListener('link-update', (e) => {
            this.updateLink(e.detail.id, e.detail.data);
        });

        window.addEventListener('link-delete', (e) => {
            this.deleteLink(e.detail.id);
        });

        // Settings changes
        window.addEventListener('settings-changed', (e) => {
            this.handleSettingsChange(e.detail.settings);
        });

        // Storage changes (for sync between tabs)
        window.addEventListener('storage', (e) => {
            if (e.key === storageManager.keys.LINKS) {
                this.loadData();
                this.render();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * Show add link modal
     */
    showAddLinkModal() {
        modalManager.showLinkForm(
            null,
            (linkData) => {
                this.addLink(linkData);
            }
        );
    }

    /**
     * Add new link
     */
    async addLink(linkData) {
        try {
            const loadingToast = showLoading('Adding link...');
            
            // Generate ID
            const link = {
                ...linkData,
                id: this.generateId(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                clickCount: 0
            };

            // Fetch preview if auto-preview is enabled
            if (settingsManager.get('autoPreview', true)) {
                try {
                    link.preview = await previewService.getPreview(link.url);
                    
                    // Use preview title if no title provided
                    if (!link.title && link.preview?.title) {
                        link.title = link.preview.title;
                    }
                    
                    // Use preview description if no description provided
                    if (!link.description && link.preview?.description) {
                        link.description = link.preview.description;
                    }
                } catch (error) {
                    console.warn('Failed to fetch preview:', error);
                }
            }

            // Add to links array
            this.links.unshift(link);
            
            // Save to storage
            storageManager.set(storageManager.keys.LINKS, this.links);
            
            // Update filtered links
            this.applyFilters();
            
            // Re-render
            this.render();
            
            hideToast(loadingToast);
            showSuccess('Link added successfully');
            
        } catch (error) {
            console.error('Failed to add link:', error);
            showError('Failed to add link: ' + error.message);
        }
    }

    /**
     * Update existing link
     */
    async updateLink(id, linkData) {
        try {
            const linkIndex = this.links.findIndex(link => link.id === id);
            
            if (linkIndex === -1) {
                throw new Error('Link not found');
            }

            const existingLink = this.links[linkIndex];
            const updatedLink = {
                ...existingLink,
                ...linkData,
                updatedAt: new Date().toISOString()
            };

            // Fetch new preview if URL changed
            if (existingLink.url !== updatedLink.url && settingsManager.get('autoPreview', true)) {
                try {
                    updatedLink.preview = await previewService.getPreview(updatedLink.url);
                } catch (error) {
                    console.warn('Failed to fetch preview:', error);
                }
            }

            // Update link
            this.links[linkIndex] = updatedLink;
            
            // Save to storage
            storageManager.set(storageManager.keys.LINKS, this.links);
            
            // Update filtered links
            this.applyFilters();
            
            // Re-render
            this.render();
            
            showSuccess('Link updated successfully');
            
        } catch (error) {
            console.error('Failed to update link:', error);
            showError('Failed to update link: ' + error.message);
        }
    }

    /**
     * Delete link
     */
    deleteLink(id) {
        try {
            const linkIndex = this.links.findIndex(link => link.id === id);
            
            if (linkIndex === -1) {
                throw new Error('Link not found');
            }

            // Remove link
            this.links.splice(linkIndex, 1);
            
            // Save to storage
            storageManager.set(storageManager.keys.LINKS, this.links);
            
            // Update filtered links
            this.applyFilters();
            
            // Re-render
            this.render();
            
        } catch (error) {
            console.error('Failed to delete link:', error);
            showError('Failed to delete link: ' + error.message);
        }
    }

    /**
     * Handle category change
     */
    handleCategoryChange(categoryId) {
        this.currentCategory = categoryId;
        this.applyFilters();
        this.render();
    }

    /**
     * Toggle search
     */
    toggleSearch() {
        const searchContainer = document.getElementById('searchContainer');
        const searchInput = document.getElementById('searchInput');
        
        if (searchContainer.classList.contains('hidden')) {
            searchContainer.classList.remove('hidden');
            searchInput.focus();
        } else {
            searchContainer.classList.add('hidden');
            searchInput.value = '';
            this.handleSearch('');
        }
    }

    /**
     * Handle search
     */
    handleSearch(query) {
        this.searchQuery = query.toLowerCase().trim();

        // Dispatch analytics event
        if (this.searchQuery) {
            window.dispatchEvent(new CustomEvent('search-performed', {
                detail: { query: this.searchQuery }
            }));
        }

        this.applyFilters();
        this.render();
        this.updateFilterDropdowns();
    }

    /**
     * Handle domain filter
     */
    handleDomainFilter(domain) {
        this.domainFilter = domain;
        this.applyFilters();
        this.render();
    }

    /**
     * Handle tag filter
     */
    handleTagFilter(tag) {
        this.tagFilter = tag;
        this.applyFilters();
        this.render();
    }

    /**
     * Clear all filters
     */
    clearAllFilters() {
        this.searchQuery = '';
        this.domainFilter = '';
        this.tagFilter = '';

        // Reset UI
        const searchInput = document.getElementById('searchInput');
        const domainFilter = document.getElementById('domainFilter');
        const tagFilter = document.getElementById('tagFilter');

        if (searchInput) searchInput.value = '';
        if (domainFilter) domainFilter.value = '';
        if (tagFilter) tagFilter.value = '';

        this.applyFilters();
        this.render();
    }

    /**
     * Toggle view mode
     */
    toggleViewMode() {
        const currentMode = settingsManager.get('viewMode', 'grid');
        const newMode = currentMode === 'grid' ? 'list' : 'grid';

        settingsManager.set('viewMode', newMode);

        // Update button icon
        const viewToggleBtn = document.getElementById('viewToggleBtn');
        const icon = viewToggleBtn?.querySelector('i');
        if (icon) {
            icon.className = newMode === 'grid' ? 'fas fa-th text-white' : 'fas fa-list text-white';
        }

        this.render();
        showInfo(`Switched to ${newMode} view`);
    }

    /**
     * Apply filters to links
     */
    applyFilters() {
        let filtered = [...this.links];

        // Category filter
        if (this.currentCategory && this.currentCategory !== 'all') {
            if (this.currentCategory === 'favorites') {
                filtered = filtered.filter(link => link.favorite);
            } else {
                filtered = filtered.filter(link => link.category === this.currentCategory);
            }
        }

        // Search filter
        if (this.searchQuery) {
            const searchFields = getConfig('SEARCH.SEARCH_FIELDS', ['title', 'description', 'url', 'tags']);

            filtered = filtered.filter(link => {
                return searchFields.some(field => {
                    const value = link[field];
                    if (Array.isArray(value)) {
                        return value.some(item => item.toLowerCase().includes(this.searchQuery));
                    }
                    return value && value.toLowerCase().includes(this.searchQuery);
                });
            });
        }

        // Domain filter
        if (this.domainFilter) {
            filtered = filtered.filter(link => {
                const domain = this.extractDomain(link.url);
                return domain === this.domainFilter;
            });
        }

        // Tag filter
        if (this.tagFilter) {
            filtered = filtered.filter(link => {
                return link.tags && link.tags.some(tag =>
                    tag.toLowerCase() === this.tagFilter.toLowerCase()
                );
            });
        }

        this.filteredLinks = filtered;
    }

    /**
     * Render the application
     */
    render() {
        this.renderLinks();
        this.updateEmptyState();
        this.updateFilterDropdowns();
        categoryManager.updateLinkCounts();
    }

    /**
     * Render links
     */
    renderLinks() {
        const container = document.getElementById('linksContainer');
        if (!container) return;

        // Clear container
        container.innerHTML = '';

        // Show loading state
        if (this.isLoading) {
            container.innerHTML = `
                <div class="col-span-full flex items-center justify-center py-12">
                    <div class="loading-spinner"></div>
                    <span class="ml-3 text-white">Loading links...</span>
                </div>
            `;
            return;
        }

        // Update container layout based on view mode
        const viewMode = settingsManager.get('viewMode', 'grid');
        if (viewMode === 'list') {
            container.className = 'space-y-4';
        } else {
            container.className = 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6';
        }

        // Render links
        const showPreviews = settingsManager.get('showLinkPreviews', true);

        this.filteredLinks.forEach((link, index) => {
            const linkCard = createLinkCard(link, {
                showPreview: showPreviews,
                viewMode: viewMode
            });

            // Add drag and drop event listeners
            this.setupDragAndDrop(linkCard, index);

            container.appendChild(linkCard);
        });

        // Add animation classes
        const cards = container.querySelectorAll('.link-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 50);
        });
    }

    /**
     * Update empty state
     */
    updateEmptyState() {
        const emptyState = document.getElementById('emptyState');
        const linksContainer = document.getElementById('linksContainer');
        
        if (!emptyState || !linksContainer) return;

        if (this.filteredLinks.length === 0 && !this.isLoading) {
            emptyState.classList.remove('hidden');
            linksContainer.classList.add('hidden');
            
            // Update empty state message based on context
            const title = emptyState.querySelector('h3');
            const description = emptyState.querySelector('p');
            
            if (this.searchQuery) {
                title.textContent = 'No Results Found';
                description.textContent = `No links match your search for "${this.searchQuery}"`;
            } else if (this.currentCategory !== 'all') {
                const category = categoryManager.getCategory(this.currentCategory);
                title.textContent = 'No Links in Category';
                description.textContent = `No links found in "${category?.name || 'this category'}"`;
            } else {
                title.textContent = 'No Links Yet';
                description.textContent = 'Start building your digital library by adding your first link';
            }
        } else {
            emptyState.classList.add('hidden');
            linksContainer.classList.remove('hidden');
        }
    }

    /**
     * Handle settings changes
     */
    handleSettingsChange(settings) {
        // Re-render if view mode changed
        if (settings.viewMode !== settingsManager.get('viewMode')) {
            this.render();
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        if (!settingsManager.get('keyboardShortcuts', true)) return;

        // Don't handle shortcuts when typing in inputs
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        const isCtrl = e.ctrlKey || e.metaKey;

        // Add new link (Ctrl+N)
        if (isCtrl && e.key === 'n') {
            e.preventDefault();
            this.showAddLinkModal();
        }

        // Search (Ctrl+F)
        if (isCtrl && e.key === 'f') {
            e.preventDefault();
            this.toggleSearch();
        }
    }

    /**
     * Show welcome message for first-time users
     */
    showWelcomeMessage() {
        const settings = storageManager.get(storageManager.keys.SETTINGS, {});
        
        if (settings.firstRun && this.links.length === 0) {
            setTimeout(() => {
                showInfo(
                    'Welcome to Link Manager Pro! Start by adding your first link.',
                    8000,
                    {
                        action: () => this.showAddLinkModal(),
                        actionText: 'Add Link'
                    }
                );
                
                // Mark as not first run
                settings.firstRun = false;
                storageManager.set(storageManager.keys.SETTINGS, settings);
            }, 1000);
        }
    }

    /**
     * Utility methods
     */
    
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Refresh data (useful for sync)
     */
    async refresh() {
        this.isLoading = true;
        this.render();
        
        try {
            await this.loadData();
            this.applyFilters();
            this.render();
        } catch (error) {
            console.error('Failed to refresh data:', error);
            showError('Failed to refresh data');
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Update filter dropdowns
     */
    updateFilterDropdowns() {
        this.updateDomainFilter();
        this.updateTagFilter();
    }

    /**
     * Update domain filter dropdown
     */
    updateDomainFilter() {
        const domainFilter = document.getElementById('domainFilter');
        if (!domainFilter) return;

        // Get unique domains from all links
        const domains = [...new Set(this.links.map(link => this.extractDomain(link.url)))];
        domains.sort();

        // Clear existing options (except first)
        while (domainFilter.children.length > 1) {
            domainFilter.removeChild(domainFilter.lastChild);
        }

        // Add domain options
        domains.forEach(domain => {
            const option = document.createElement('option');
            option.value = domain;
            option.textContent = domain;
            if (domain === this.domainFilter) {
                option.selected = true;
            }
            domainFilter.appendChild(option);
        });
    }

    /**
     * Update tag filter dropdown
     */
    updateTagFilter() {
        const tagFilter = document.getElementById('tagFilter');
        if (!tagFilter) return;

        // Get unique tags from all links
        const tags = new Set();
        this.links.forEach(link => {
            if (link.tags) {
                link.tags.forEach(tag => tags.add(tag));
            }
        });
        const sortedTags = [...tags].sort();

        // Clear existing options (except first)
        while (tagFilter.children.length > 1) {
            tagFilter.removeChild(tagFilter.lastChild);
        }

        // Add tag options
        sortedTags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag;
            option.textContent = tag;
            if (tag === this.tagFilter) {
                option.selected = true;
            }
            tagFilter.appendChild(option);
        });
    }

    /**
     * Setup drag and drop for link cards
     */
    setupDragAndDrop(card, index) {
        if (!getConfig('UI.FEATURES.DRAG_DROP', true)) return;

        card.addEventListener('dragstart', (e) => {
            this.draggedElement = { card, index };
            card.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
        });

        card.addEventListener('dragend', (e) => {
            card.classList.remove('dragging');
            this.draggedElement = null;
        });

        card.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });

        card.addEventListener('drop', (e) => {
            e.preventDefault();
            if (this.draggedElement && this.draggedElement.index !== index) {
                this.reorderLinks(this.draggedElement.index, index);
            }
        });
    }

    /**
     * Reorder links array
     */
    reorderLinks(fromIndex, toIndex) {
        const movedLink = this.filteredLinks.splice(fromIndex, 1)[0];
        this.filteredLinks.splice(toIndex, 0, movedLink);

        // Update the main links array to match the new order
        // This is a simplified approach - in a real app you might want more sophisticated ordering
        this.links = [...this.filteredLinks];

        // Save to storage
        storageManager.set(storageManager.keys.LINKS, this.links);

        // Re-render
        this.render();

        showSuccess('Links reordered');
    }

    /**
     * Extract domain from URL
     */
    extractDomain(url) {
        try {
            return new URL(url).hostname.replace(/^www\./, '');
        } catch {
            return 'unknown';
        }
    }

    /**
     * Get app statistics
     */
    getStats() {
        const now = new Date();
        const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        return {
            totalLinks: this.links.length,
            favoriteLinks: this.links.filter(link => link.favorite).length,
            categorizedLinks: this.links.filter(link => link.category).length,
            linksAddedToday: this.links.filter(link => new Date(link.createdAt) > dayAgo).length,
            linksAddedThisWeek: this.links.filter(link => new Date(link.createdAt) > weekAgo).length,
            linksAddedThisMonth: this.links.filter(link => new Date(link.createdAt) > monthAgo).length,
            totalClicks: this.links.reduce((sum, link) => sum + (link.clickCount || 0), 0),
            categories: categoryManager.getCategories().length - 1, // Exclude 'all'
            lastUpdated: this.links.length > 0 ? Math.max(...this.links.map(link => new Date(link.updatedAt || link.createdAt))) : null
        };
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.linkManagerApp = new LinkManagerApp();
});

// Handle page visibility changes (for potential sync)
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.linkManagerApp) {
        // Optionally refresh data when page becomes visible
        // window.linkManagerApp.refresh();
    }
});
