// Toast Notification System
class ToastManager {
    constructor() {
        this.container = document.getElementById('toastContainer');
        this.toasts = new Map();
        this.toastId = 0;
    }

    /**
     * Show a toast notification
     * @param {string} message - The message to display
     * @param {string} type - Toast type: 'success', 'error', 'warning', 'info'
     * @param {number} duration - Duration in milliseconds (0 for persistent)
     * @param {Object} options - Additional options
     */
    show(message, type = 'info', duration = null, options = {}) {
        const id = ++this.toastId;
        const toastDuration = duration ?? getConfig('UI.TOAST_DURATION', 4000);
        
        const toast = this.createToastElement(id, message, type, options);
        this.container.appendChild(toast);
        this.toasts.set(id, toast);

        // Trigger animation
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // Auto-remove after duration (if not persistent)
        if (toastDuration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, toastDuration);
        }

        return id;
    }

    /**
     * Hide a specific toast
     * @param {number} id - Toast ID
     */
    hide(id) {
        const toast = this.toasts.get(id);
        if (!toast) return;

        toast.classList.remove('show');
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.toasts.delete(id);
        }, 300);
    }

    /**
     * Hide all toasts
     */
    hideAll() {
        this.toasts.forEach((toast, id) => {
            this.hide(id);
        });
    }

    /**
     * Create toast element
     * @private
     */
    createToastElement(id, message, type, options) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.setAttribute('data-toast-id', id);

        const icon = this.getTypeIcon(type);
        const showCloseButton = options.closable !== false;
        const showAction = options.action && options.actionText;

        toast.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <i class="${icon} text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-white font-medium">${this.escapeHtml(message)}</p>
                    ${options.description ? `<p class="text-gray-300 text-sm mt-1">${this.escapeHtml(options.description)}</p>` : ''}
                    ${showAction ? `
                        <button class="mt-2 text-sm text-purple-300 hover:text-purple-200 underline" 
                                onclick="toastManager.handleAction(${id}, '${options.action}')">
                            ${this.escapeHtml(options.actionText)}
                        </button>
                    ` : ''}
                </div>
                ${showCloseButton ? `
                    <button class="flex-shrink-0 text-gray-400 hover:text-white transition-colors" 
                            onclick="toastManager.hide(${id})">
                        <i class="fas fa-times"></i>
                    </button>
                ` : ''}
            </div>
        `;

        // Add click handler for dismissal (if enabled)
        if (options.clickToDismiss !== false) {
            toast.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    this.hide(id);
                }
            });
        }

        return toast;
    }

    /**
     * Handle action button clicks
     * @private
     */
    handleAction(toastId, action) {
        if (typeof action === 'function') {
            action();
        } else if (typeof action === 'string') {
            // Dispatch custom event
            window.dispatchEvent(new CustomEvent('toast-action', {
                detail: { action, toastId }
            }));
        }
        this.hide(toastId);
    }

    /**
     * Get icon for toast type
     * @private
     */
    getTypeIcon(type) {
        const icons = {
            success: 'fas fa-check-circle text-green-400',
            error: 'fas fa-exclamation-circle text-red-400',
            warning: 'fas fa-exclamation-triangle text-yellow-400',
            info: 'fas fa-info-circle text-blue-400'
        };
        return icons[type] || icons.info;
    }

    /**
     * Escape HTML to prevent XSS
     * @private
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Convenience methods
    success(message, duration, options) {
        return this.show(message, 'success', duration, options);
    }

    error(message, duration, options) {
        return this.show(message, 'error', duration, options);
    }

    warning(message, duration, options) {
        return this.show(message, 'warning', duration, options);
    }

    info(message, duration, options) {
        return this.show(message, 'info', duration, options);
    }

    /**
     * Show loading toast with spinner
     */
    loading(message = 'Loading...', options = {}) {
        const loadingOptions = {
            ...options,
            closable: false,
            clickToDismiss: false
        };

        const id = this.show(message, 'info', 0, loadingOptions);
        
        // Add spinner to the toast
        const toast = this.toasts.get(id);
        const iconElement = toast.querySelector('i');
        iconElement.className = 'loading-spinner';

        return id;
    }

    /**
     * Update an existing toast
     */
    update(id, message, type, options = {}) {
        const toast = this.toasts.get(id);
        if (!toast) return;

        const messageElement = toast.querySelector('p');
        if (messageElement) {
            messageElement.textContent = message;
        }

        // Update type class
        toast.className = `toast ${type} show`;

        // Update icon
        const iconElement = toast.querySelector('i');
        if (iconElement) {
            iconElement.className = this.getTypeIcon(type);
        }
    }

    /**
     * Show confirmation toast with action buttons
     */
    confirm(message, onConfirm, onCancel, options = {}) {
        const confirmOptions = {
            ...options,
            closable: false,
            clickToDismiss: false
        };

        const toast = document.createElement('div');
        toast.className = 'toast warning show';

        toast.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <i class="fas fa-question-circle text-yellow-400 text-lg"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-white font-medium">${this.escapeHtml(message)}</p>
                    <div class="flex space-x-2 mt-3">
                        <button class="confirm-btn glass-button-primary px-4 py-2 rounded-lg text-sm text-white font-medium">
                            Confirm
                        </button>
                        <button class="cancel-btn glass-button px-4 py-2 rounded-lg text-sm text-white font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        const id = ++this.toastId;
        this.container.appendChild(toast);
        this.toasts.set(id, toast);

        // Add event listeners
        toast.querySelector('.confirm-btn').addEventListener('click', () => {
            if (onConfirm) onConfirm();
            this.hide(id);
        });

        toast.querySelector('.cancel-btn').addEventListener('click', () => {
            if (onCancel) onCancel();
            this.hide(id);
        });

        return id;
    }
}

// Create global instance
const toastManager = new ToastManager();

// Global convenience functions
window.showToast = (message, type, duration, options) => toastManager.show(message, type, duration, options);
window.showSuccess = (message, duration, options) => toastManager.success(message, duration, options);
window.showError = (message, duration, options) => toastManager.error(message, duration, options);
window.showWarning = (message, duration, options) => toastManager.warning(message, duration, options);
window.showInfo = (message, duration, options) => toastManager.info(message, duration, options);
window.showLoading = (message, options) => toastManager.loading(message, options);
window.hideToast = (id) => toastManager.hide(id);
window.hideAllToasts = () => toastManager.hideAll();
