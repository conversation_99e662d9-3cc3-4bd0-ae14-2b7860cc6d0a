// Analytics Service for Link Manager Pro
class AnalyticsService {
    constructor() {
        this.enabled = getConfig('UI.FEATURES.ANALYTICS', true);
        this.analytics = this.loadAnalytics();
        this.setupEventListeners();
        this.startPeriodicUpdates();
    }

    /**
     * Load analytics data from storage
     */
    loadAnalytics() {
        return storageManager.get('analytics_data', {
            totalClicks: 0,
            linkClicks: {},
            dailyStats: {},
            weeklyStats: {},
            monthlyStats: {},
            topDomains: {},
            topCategories: {},
            topTags: {},
            searchQueries: {},
            lastUpdated: new Date().toISOString()
        });
    }

    /**
     * Save analytics data
     */
    saveAnalytics() {
        this.analytics.lastUpdated = new Date().toISOString();
        storageManager.set('analytics_data', this.analytics);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.enabled) return;

        // Track link clicks
        window.addEventListener('link-click', (e) => {
            this.trackLinkClick(e.detail.linkId, e.detail.url);
        });

        // Track searches
        window.addEventListener('search-performed', (e) => {
            this.trackSearch(e.detail.query);
        });

        // Track link additions
        window.addEventListener('link-add', (e) => {
            this.trackLinkAdd(e.detail.data);
        });

        // Track category usage
        window.addEventListener('category-changed', (e) => {
            this.trackCategoryView(e.detail.categoryId);
        });

        // Show analytics panel toggle
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                this.toggleAnalyticsPanel();
            }
        });
    }

    /**
     * Track link click
     */
    trackLinkClick(linkId, url) {
        if (!this.enabled) return;

        this.analytics.totalClicks++;
        
        // Track individual link clicks
        if (!this.analytics.linkClicks[linkId]) {
            this.analytics.linkClicks[linkId] = 0;
        }
        this.analytics.linkClicks[linkId]++;

        // Track domain clicks
        const domain = this.extractDomain(url);
        if (!this.analytics.topDomains[domain]) {
            this.analytics.topDomains[domain] = 0;
        }
        this.analytics.topDomains[domain]++;

        // Track daily stats
        const today = new Date().toISOString().split('T')[0];
        if (!this.analytics.dailyStats[today]) {
            this.analytics.dailyStats[today] = { clicks: 0, searches: 0, additions: 0 };
        }
        this.analytics.dailyStats[today].clicks++;

        this.saveAnalytics();
        this.updateQuickStats();
    }

    /**
     * Track search query
     */
    trackSearch(query) {
        if (!this.enabled || !query.trim()) return;

        const normalizedQuery = query.toLowerCase().trim();
        
        if (!this.analytics.searchQueries[normalizedQuery]) {
            this.analytics.searchQueries[normalizedQuery] = 0;
        }
        this.analytics.searchQueries[normalizedQuery]++;

        // Track daily search stats
        const today = new Date().toISOString().split('T')[0];
        if (!this.analytics.dailyStats[today]) {
            this.analytics.dailyStats[today] = { clicks: 0, searches: 0, additions: 0 };
        }
        this.analytics.dailyStats[today].searches++;

        this.saveAnalytics();
    }

    /**
     * Track link addition
     */
    trackLinkAdd(linkData) {
        if (!this.enabled) return;

        // Track category usage
        if (linkData.category) {
            if (!this.analytics.topCategories[linkData.category]) {
                this.analytics.topCategories[linkData.category] = 0;
            }
            this.analytics.topCategories[linkData.category]++;
        }

        // Track tag usage
        if (linkData.tags && linkData.tags.length > 0) {
            linkData.tags.forEach(tag => {
                const normalizedTag = tag.toLowerCase().trim();
                if (!this.analytics.topTags[normalizedTag]) {
                    this.analytics.topTags[normalizedTag] = 0;
                }
                this.analytics.topTags[normalizedTag]++;
            });
        }

        // Track daily addition stats
        const today = new Date().toISOString().split('T')[0];
        if (!this.analytics.dailyStats[today]) {
            this.analytics.dailyStats[today] = { clicks: 0, searches: 0, additions: 0 };
        }
        this.analytics.dailyStats[today].additions++;

        this.saveAnalytics();
        this.updateQuickStats();
    }

    /**
     * Track category view
     */
    trackCategoryView(categoryId) {
        if (!this.enabled) return;

        if (!this.analytics.topCategories[categoryId]) {
            this.analytics.topCategories[categoryId] = 0;
        }
        this.analytics.topCategories[categoryId]++;

        this.saveAnalytics();
    }

    /**
     * Get analytics summary
     */
    getAnalyticsSummary() {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        const today = new Date().toISOString().split('T')[0];
        const todayStats = this.analytics.dailyStats[today] || { clicks: 0, searches: 0, additions: 0 };

        // Get top items
        const topDomains = this.getTopItems(this.analytics.topDomains, 5);
        const topCategories = this.getTopItems(this.analytics.topCategories, 5);
        const topTags = this.getTopItems(this.analytics.topTags, 5);
        const topSearches = this.getTopItems(this.analytics.searchQueries, 5);

        // Get most clicked links
        const mostClickedLinks = this.getMostClickedLinks(5);

        // Calculate trends
        const weeklyTrend = this.calculateWeeklyTrend();
        const monthlyTrend = this.calculateMonthlyTrend();

        return {
            overview: {
                totalLinks: links.length,
                totalClicks: this.analytics.totalClicks,
                todayClicks: todayStats.clicks,
                todaySearches: todayStats.searches,
                todayAdditions: todayStats.additions,
                averageClicksPerDay: this.calculateAverageClicksPerDay(),
                mostActiveDay: this.getMostActiveDay()
            },
            topItems: {
                domains: topDomains,
                categories: topCategories,
                tags: topTags,
                searches: topSearches,
                links: mostClickedLinks
            },
            trends: {
                weekly: weeklyTrend,
                monthly: monthlyTrend
            },
            lastUpdated: this.analytics.lastUpdated
        };
    }

    /**
     * Get most clicked links
     */
    getMostClickedLinks(limit = 5) {
        const links = storageManager.get(storageManager.keys.LINKS, []);
        
        return links
            .map(link => ({
                ...link,
                clicks: this.analytics.linkClicks[link.id] || 0
            }))
            .sort((a, b) => b.clicks - a.clicks)
            .slice(0, limit);
    }

    /**
     * Get top items from analytics object
     */
    getTopItems(analyticsObj, limit = 5) {
        return Object.entries(analyticsObj)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([key, value]) => ({ name: key, count: value }));
    }

    /**
     * Calculate weekly trend
     */
    calculateWeeklyTrend() {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        
        const weeklyData = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const stats = this.analytics.dailyStats[dateStr] || { clicks: 0, searches: 0, additions: 0 };
            
            weeklyData.push({
                date: dateStr,
                day: date.toLocaleDateString('en', { weekday: 'short' }),
                ...stats
            });
        }
        
        return weeklyData;
    }

    /**
     * Calculate monthly trend
     */
    calculateMonthlyTrend() {
        const monthAgo = new Date();
        monthAgo.setDate(monthAgo.getDate() - 30);
        
        let totalClicks = 0;
        let totalSearches = 0;
        let totalAdditions = 0;
        
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const stats = this.analytics.dailyStats[dateStr] || { clicks: 0, searches: 0, additions: 0 };
            
            totalClicks += stats.clicks;
            totalSearches += stats.searches;
            totalAdditions += stats.additions;
        }
        
        return {
            totalClicks,
            totalSearches,
            totalAdditions,
            averageClicksPerDay: totalClicks / 30,
            averageSearchesPerDay: totalSearches / 30,
            averageAdditionsPerDay: totalAdditions / 30
        };
    }

    /**
     * Calculate average clicks per day
     */
    calculateAverageClicksPerDay() {
        const days = Object.keys(this.analytics.dailyStats);
        if (days.length === 0) return 0;
        
        const totalClicks = days.reduce((sum, day) => {
            return sum + (this.analytics.dailyStats[day].clicks || 0);
        }, 0);
        
        return Math.round(totalClicks / days.length * 10) / 10;
    }

    /**
     * Get most active day
     */
    getMostActiveDay() {
        let maxClicks = 0;
        let mostActiveDay = null;
        
        Object.entries(this.analytics.dailyStats).forEach(([date, stats]) => {
            if (stats.clicks > maxClicks) {
                maxClicks = stats.clicks;
                mostActiveDay = date;
            }
        });
        
        return mostActiveDay ? {
            date: mostActiveDay,
            clicks: maxClicks,
            formatted: new Date(mostActiveDay).toLocaleDateString()
        } : null;
    }

    /**
     * Update quick stats display
     */
    updateQuickStats() {
        const quickStats = document.getElementById('quickStats');
        if (!quickStats) return;

        const summary = this.getAnalyticsSummary();
        
        quickStats.innerHTML = `
            <div>Total Clicks: <span class="text-white font-medium">${summary.overview.totalClicks}</span></div>
            <div>Today: <span class="text-white font-medium">${summary.overview.todayClicks} clicks</span></div>
            <div>Avg/Day: <span class="text-white font-medium">${summary.overview.averageClicksPerDay}</span></div>
            <div>Links: <span class="text-white font-medium">${summary.overview.totalLinks}</span></div>
        `;
    }

    /**
     * Toggle analytics panel
     */
    toggleAnalyticsPanel() {
        const panel = document.getElementById('analyticsPanel');
        if (!panel) return;

        if (panel.classList.contains('hidden')) {
            this.updateQuickStats();
            panel.classList.remove('hidden');
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                panel.classList.add('hidden');
            }, 10000);
        } else {
            panel.classList.add('hidden');
        }
    }

    /**
     * Show detailed analytics modal
     */
    showDetailedAnalytics() {
        const summary = this.getAnalyticsSummary();
        
        const content = `
            <div class="space-y-6 max-h-96 overflow-y-auto">
                <!-- Overview -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Overview</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="glass-card p-4 rounded-xl">
                            <div class="text-2xl font-bold text-white">${summary.overview.totalClicks}</div>
                            <div class="text-gray-300 text-sm">Total Clicks</div>
                        </div>
                        <div class="glass-card p-4 rounded-xl">
                            <div class="text-2xl font-bold text-white">${summary.overview.totalLinks}</div>
                            <div class="text-gray-300 text-sm">Total Links</div>
                        </div>
                        <div class="glass-card p-4 rounded-xl">
                            <div class="text-2xl font-bold text-white">${summary.overview.todayClicks}</div>
                            <div class="text-gray-300 text-sm">Today's Clicks</div>
                        </div>
                        <div class="glass-card p-4 rounded-xl">
                            <div class="text-2xl font-bold text-white">${summary.overview.averageClicksPerDay}</div>
                            <div class="text-gray-300 text-sm">Avg Clicks/Day</div>
                        </div>
                    </div>
                </div>

                <!-- Top Items -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Top Performers</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-white font-medium mb-2">Most Clicked Links</h4>
                            <div class="space-y-2">
                                ${summary.topItems.links.slice(0, 3).map(link => `
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-300 truncate">${link.title || link.url}</span>
                                        <span class="text-white font-medium">${link.clicks}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div>
                            <h4 class="text-white font-medium mb-2">Top Domains</h4>
                            <div class="space-y-2">
                                ${summary.topItems.domains.slice(0, 3).map(domain => `
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-300">${domain.name}</span>
                                        <span class="text-white font-medium">${domain.count}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Trend -->
                <div>
                    <h3 class="text-lg font-semibold text-white mb-4">Weekly Activity</h3>
                    <div class="grid grid-cols-7 gap-2">
                        ${summary.trends.weekly.map(day => `
                            <div class="text-center">
                                <div class="text-xs text-gray-400 mb-1">${day.day}</div>
                                <div class="h-16 bg-gradient-to-t from-purple-500 to-blue-500 rounded opacity-${Math.min(100, day.clicks * 10)} flex items-end justify-center">
                                    <span class="text-white text-xs font-medium mb-1">${day.clicks}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        modalManager.show({
            title: 'Analytics Dashboard',
            content,
            size: 'xl',
            actions: [
                { text: 'Close', className: 'glass-button text-white', action: 'close' }
            ]
        });
    }

    /**
     * Start periodic updates
     */
    startPeriodicUpdates() {
        if (!this.enabled) return;

        // Update quick stats every 30 seconds
        setInterval(() => {
            this.updateQuickStats();
        }, 30000);
    }

    /**
     * Utility functions
     */
    
    extractDomain(url) {
        try {
            return new URL(url).hostname.replace(/^www\./, '');
        } catch {
            return 'unknown';
        }
    }

    /**
     * Export analytics data
     */
    exportAnalytics() {
        const summary = this.getAnalyticsSummary();
        const blob = new Blob([JSON.stringify(summary, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * Clear analytics data
     */
    clearAnalytics() {
        this.analytics = {
            totalClicks: 0,
            linkClicks: {},
            dailyStats: {},
            weeklyStats: {},
            monthlyStats: {},
            topDomains: {},
            topCategories: {},
            topTags: {},
            searchQueries: {},
            lastUpdated: new Date().toISOString()
        };
        this.saveAnalytics();
        this.updateQuickStats();
        showSuccess('Analytics data cleared');
    }
}

// Create global instance
const analyticsService = new AnalyticsService();
